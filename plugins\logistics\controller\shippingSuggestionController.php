<?php

namespace plugins\logistics\controller;

use plugins\logistics\models\shippingSuggestionModel;
use plugins\logistics\controller\baseController;

/**
 * 发货建议控制器
 * 处理发货建议的列表查询、状态变更等功能
 */
class shippingSuggestionController extends baseController
{
    /**
     * 获取发货建议列表
     * @return void
     */
    public function getList()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $site = $_POST['site'] ?? '';
            $suggestionDate = $_POST['suggestion_date'] ?? date('Y-m-d');
            $shippingMethod = $_POST['shipping_method'] ?? ''; // air/sea
            $urgentFlag = $_POST['urgent_flag'] ?? ''; // 0/1
            $status = $_POST['status'] ?? ''; // pending/processing/completed/ignored
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 20);

            $filters = [
                'sku' => $sku,
                'site' => $site,
                'suggestion_date' => $suggestionDate,
                'shipping_method' => $shippingMethod,
                'urgent_flag' => $urgentFlag,
                'status' => $status
            ];

            $shippingModel = new shippingSuggestionModel();
            $result = $shippingModel->getShippingSuggestionList($filters, $page, $pageSize);

            returnSuccess($result, '获取成功');
        } catch (\Exception $e) {
            returnError('获取发货建议列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取发货建议详情
     * @return void
     */
    public function getDetail()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            
            if (!$id) {
                returnError('ID参数不能为空');
            }

            $shippingModel = new shippingSuggestionModel();
            $detail = $shippingModel->getSuggestionDetail($id);

            if (!$detail) {
                returnError('发货建议不存在');
            }

            returnSuccess($detail, '获取成功');
        } catch (\Exception $e) {
            returnError('获取发货建议详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新发货建议状态
     * @return void
     */
    public function updateStatus()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $status = $_POST['status'] ?? '';

            if (!$id) {
                returnError('ID参数不能为空');
            }

            $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
            if (!in_array($status, $allowedStatus)) {
                returnError('状态参数错误，允许的状态：' . implode(', ', $allowedStatus));
            }

            $shippingModel = new shippingSuggestionModel();
            
            // 检查建议是否存在
            $detail = $shippingModel->getSuggestionDetail($id);
            if (!$detail) {
                returnError('发货建议不存在');
            }

            // 状态变更逻辑检查
            if ($detail['status'] === 'completed' && $status !== 'completed') {
                returnError('已完成的发货建议无法变更为其他状态');
            }

            $result = $shippingModel->updateSuggestionStatus($id, $status);
            
            if ($result) {
                returnSuccess([], '状态更新成功');
            } else {
                returnError('状态更新失败');
            }
        } catch (\Exception $e) {
            returnError('更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新发货建议状态
     * @return void
     */
    public function batchUpdateStatus()
    {
        try {
            $ids = $_POST['ids'] ?? [];
            $status = $_POST['status'] ?? '';

            if (empty($ids) || !is_array($ids)) {
                returnError('ID列表不能为空');
            }

            $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
            if (!in_array($status, $allowedStatus)) {
                returnError('状态参数错误，允许的状态：' . implode(', ', $allowedStatus));
            }

            $shippingModel = new shippingSuggestionModel();
            
            // 批量状态更新
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            
            foreach ($ids as $id) {
                try {
                    $result = $shippingModel->updateSuggestionStatus($id, $status);
                    if ($result) {
                        $successCount++;
                    } else {
                        $errorCount++;
                        $errors[] = "ID {$id} 更新失败";
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "ID {$id} 更新失败: " . $e->getMessage();
                }
            }
            
            $result = [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];
            
            if ($errorCount > 0) {
                returnSuccess($result, "批量更新完成，成功 {$successCount} 条，失败 {$errorCount} 条");
            } else {
                returnSuccess($result, '批量状态更新成功');
            }
        } catch (\Exception $e) {
            returnError('批量更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取发货建议统计
     * @return void
     */
    public function getStatistics()
    {
        try {
            $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $_POST['end_date'] ?? date('Y-m-d');

            $shippingModel = new shippingSuggestionModel();
            $statistics = $shippingModel->getSuggestionStatistics($startDate, $endDate);

            returnSuccess($statistics, '获取统计成功');
        } catch (\Exception $e) {
            returnError('获取统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成发货建议
     * @return void
     */
    public function generateSuggestion()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $weekType = $_POST['week_type'] ?? 'current'; // current/next

            $shippingModel = new shippingSuggestionModel();
            $result = $shippingModel->generateShippingSuggestion($calculationDate, $weekType);

            if ($result['success']) {
                returnSuccess($result, '发货建议生成成功');
            } else {
                returnError($result['message'] ?? '发货建议生成失败');
            }
        } catch (\Exception $e) {
            returnError('生成发货建议失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取紧急发货建议
     * @return void
     */
    public function getUrgentList()
    {
        try {
            $suggestionDate = $_POST['suggestion_date'] ?? date('Y-m-d');
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 20);

            $filters = [
                'suggestion_date' => $suggestionDate,
                'urgent_flag' => 1,
                'status' => 'pending'
            ];

            $shippingModel = new shippingSuggestionModel();
            $result = $shippingModel->getShippingSuggestionList($filters, $page, $pageSize);

            returnSuccess($result, '获取紧急发货建议成功');
        } catch (\Exception $e) {
            returnError('获取紧急发货建议失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取店铺分配详情
     * @return void
     */
    public function getShopAllocation()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            
            if (!$id) {
                returnError('ID参数不能为空');
            }

            $shippingModel = new shippingSuggestionModel();
            $detail = $shippingModel->getSuggestionDetail($id);

            if (!$detail) {
                returnError('发货建议不存在');
            }

            // 解析店铺分配数据
            $attachData = $detail['attach_data'] ?? [];
            
            returnSuccess([
                'suggestion_info' => [
                    'id' => $detail['id'],
                    'sku' => $detail['sku'],
                    'site' => $detail['site'],
                    'suggested_stock' => $detail['suggested_stock'],
                    'shipping_method' => $detail['shipping_method'],
                    'urgent_flag' => $detail['urgent_flag']
                ],
                'shop_allocation' => $attachData
            ], '获取店铺分配成功');
        } catch (\Exception $e) {
            returnError('获取店铺分配失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理过期发货建议
     * @return void
     */
    public function cleanExpired()
    {
        try {
            $days = (int)($_POST['days'] ?? 30);

            $shippingModel = new shippingSuggestionModel();
            $deletedCount = $shippingModel->cleanExpiredSuggestions($days);

            returnSuccess([
                'deleted_count' => $deletedCount
            ], "清理完成，删除了 {$deletedCount} 条过期记录");
        } catch (\Exception $e) {
            returnError('清理过期记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出发货建议
     * @return void
     */
    public function export()
    {
        try {
            $suggestionDate = $_POST['suggestion_date'] ?? date('Y-m-d');
            $status = $_POST['status'] ?? '';
            $shippingMethod = $_POST['shipping_method'] ?? '';
            $urgentFlag = $_POST['urgent_flag'] ?? '';

            $filters = [
                'suggestion_date' => $suggestionDate,
                'status' => $status,
                'shipping_method' => $shippingMethod,
                'urgent_flag' => $urgentFlag
            ];

            $shippingModel = new shippingSuggestionModel();
            $result = $shippingModel->getShippingSuggestionList($filters, 1, 10000); // 导出所有数据

            if (empty($result['list'])) {
                returnError('没有数据可导出');
            }

            // 设置CSV头部
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="shipping_suggestion_' . $suggestionDate . '.csv"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');

            // 输出BOM以支持中文
            echo "\xEF\xBB\xBF";

            $output = fopen('php://output', 'w');

            // CSV头部
            $headers = [
                'SKU', '站点', 'ASIN', '产品名称', '当前库存', '建议库存',
                '发货方式', '发货路线', '紧急标志', '状态', '建议日期', '店铺分配详情'
            ];
            fputcsv($output, $headers);

            // 输出数据
            foreach ($result['list'] as $item) {
                $attachData = $item['attach_data'] ?? [];
                $shopAllocationSummary = '';

                if (!empty($attachData['shop_allocations'])) {
                    $allocations = [];
                    foreach ($attachData['shop_allocations'] as $allocation) {
                        $allocations[] = $allocation['shop_code'] . ':' . $allocation['allocated_quantity'];
                    }
                    $shopAllocationSummary = implode('; ', $allocations);
                }

                $row = [
                    $item['sku'],
                    $item['site'],
                    $item['asin'],
                    $item['product_name'],
                    $item['current_stock'],
                    $item['suggested_stock'],
                    $item['shipping_method'],
                    $item['shipping_route'],
                    $item['urgent_flag'] ? '是' : '否',
                    $item['status'],
                    $item['suggestion_date'],
                    $shopAllocationSummary
                ];
                fputcsv($output, $row);
            }

            fclose($output);
            exit;
        } catch (\Exception $e) {
            returnError('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 重新计算发货建议
     * @return void
     */
    public function recalculate()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $weekType = $_POST['week_type'] ?? 'current';

            $shippingModel = new shippingSuggestionModel();
            $result = $shippingModel->generateShippingSuggestion($calculationDate, $weekType);

            if ($result['success']) {
                returnSuccess($result, '重新计算发货建议成功');
            } else {
                returnError($result['message'] ?? '重新计算发货建议失败');
            }
        } catch (\Exception $e) {
            returnError('重新计算发货建议失败: ' . $e->getMessage());
        }
    }
}
