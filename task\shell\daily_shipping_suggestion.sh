#!/bin/bash

# 发货建议每日生成脚本
# 功能：基于备货建议生成发货建议，包含店铺分配逻辑
# 作者：AI助手
# 创建时间：2025-07-16

# 设置脚本目录和日志文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"
LOG_FILE="$LOG_DIR/shipping_suggestion_$(date +%Y%m%d).log"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    local exit_code=$1
    local line_number=$2
    log_error "脚本在第 $line_number 行发生错误，退出码：$exit_code"
    exit $exit_code
}

# 设置错误处理
trap 'handle_error $? $LINENO' ERR
set -e

# 配置参数
CALCULATION_DATE="${1:-$(date +%Y-%m-%d)}"
WEEK_TYPE="${2:-current}"  # current/next
MAX_ITERATIONS=1000
ITERATION_COUNT=0
SLEEP_INTERVAL=3

log_info "========== 发货建议生成开始 =========="
log_info "计算日期: $CALCULATION_DATE"
log_info "周类型: $WEEK_TYPE"
log_info "最大迭代次数: $MAX_ITERATIONS"

# 验证日期格式
if ! [[ "$CALCULATION_DATE" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
    log_error "日期格式错误: $CALCULATION_DATE，应为 YYYY-MM-DD 格式"
    exit 1
fi

# 验证周类型
if [[ "$WEEK_TYPE" != "current" && "$WEEK_TYPE" != "next" ]]; then
    log_error "周类型错误: $WEEK_TYPE，应为 current 或 next"
    exit 1
fi

# 检查PHP环境
if ! command -v php &> /dev/null; then
    log_error "PHP 未安装或不在 PATH 中"
    exit 1
fi

# 检查项目文件
CONTROLLER_FILE="$PROJECT_ROOT/task/controller/logisticsController.php"
if [[ ! -f "$CONTROLLER_FILE" ]]; then
    log_error "控制器文件不存在: $CONTROLLER_FILE"
    exit 1
fi

# 发货建议生成函数
generate_shipping_suggestion() {
    local calculation_date="$1"
    local week_type="$2"
    
    log_info "开始生成发货建议..."
    
    while [[ $ITERATION_COUNT -lt $MAX_ITERATIONS ]]; do
        ITERATION_COUNT=$((ITERATION_COUNT + 1))
        
        log_info "第 $ITERATION_COUNT 次迭代开始"
        
        # 调用PHP控制器方法
        local response=$(php -r "
            \$_POST['token'] = '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0';
            \$_POST['calculation_date'] = '$calculation_date';
            \$_POST['week_type'] = '$week_type';
            
            require_once '$PROJECT_ROOT/index.php';
            require_once '$PROJECT_ROOT/task/controller/logisticsController.php';
            
            \$controller = new task\controller\logisticsController();
            \$controller->dailyShippingSuggestion();
        " 2>&1)
        
        # 检查PHP执行结果
        if [[ $? -ne 0 ]]; then
            log_error "PHP执行失败: $response"
            return 1
        fi
        
        log_info "PHP响应: $response"
        
        # 解析响应判断是否继续
        if echo "$response" | grep -q '"code":2' || echo "$response" | grep -q '"code":"2"'; then
            log_info "发货建议生成完成"
            break
        elif echo "$response" | grep -q '"code":1' || echo "$response" | grep -q '"code":"1"'; then
            log_info "继续处理下一批数据，等待 $SLEEP_INTERVAL 秒..."
            sleep $SLEEP_INTERVAL
        elif echo "$response" | grep -q '"code":false' || echo "$response" | grep -q '"code":"false"'; then
            log_error "发货建议生成失败: $response"
            return 1
        else
            log_warn "未知响应格式: $response"
            sleep $SLEEP_INTERVAL
        fi
    done
    
    if [[ $ITERATION_COUNT -ge $MAX_ITERATIONS ]]; then
        log_error "达到最大迭代次数 $MAX_ITERATIONS，可能存在问题"
        return 1
    fi
    
    return 0
}

# 清理过期数据函数
cleanup_expired_data() {
    log_info "开始清理过期发货建议数据..."
    
    local response=$(php -r "
        require_once '$PROJECT_ROOT/plugins/logistics/models/ShippingSuggestionModel.php';
        
        try {
            \$model = new ShippingSuggestionModel();
            \$deletedCount = \$model->cleanExpiredSuggestions(30);
            echo json_encode(['success' => true, 'deleted_count' => \$deletedCount]);
        } catch (Exception \$e) {
            echo json_encode(['success' => false, 'error' => \$e->getMessage()]);
        }
    " 2>&1)
    
    if echo "$response" | grep -q '"success":true'; then
        log_info "过期数据清理完成: $response"
    else
        log_warn "过期数据清理失败: $response"
    fi
}

# 生成统计报告函数
generate_statistics() {
    local calculation_date="$1"
    
    log_info "生成发货建议统计报告..."
    
    local response=$(php -r "
        require_once '$PROJECT_ROOT/plugins/logistics/models/ShippingSuggestionModel.php';
        
        try {
            \$model = new ShippingSuggestionModel();
            \$stats = \$model->getSuggestionStatistics('$calculation_date', '$calculation_date');
            echo json_encode(\$stats);
        } catch (Exception \$e) {
            echo json_encode(['error' => \$e->getMessage()]);
        }
    " 2>&1)
    
    log_info "统计报告: $response"
}

# 主执行流程
main() {
    local start_time=$(date +%s)
    
    # 1. 生成发货建议
    if generate_shipping_suggestion "$CALCULATION_DATE" "$WEEK_TYPE"; then
        log_info "发货建议生成成功"
    else
        log_error "发货建议生成失败"
        exit 1
    fi
    
    # 2. 清理过期数据
    cleanup_expired_data
    
    # 3. 生成统计报告
    generate_statistics "$CALCULATION_DATE"
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "========== 发货建议生成完成 =========="
    log_info "总耗时: ${duration} 秒"
    log_info "总迭代次数: $ITERATION_COUNT"
    log_info "日志文件: $LOG_FILE"
}

# 执行主函数
main "$@"
