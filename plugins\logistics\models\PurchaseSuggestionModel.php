<?php

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use DateInterval;
use DateTime;
use Exception;
use plugins\logistics\utils\TimeCalculator;

require_once dirname(__FILE__) . '/../utils/TimeCalculator.php';

/**
 * 采购建议模型
 * 实现基于SKU+站点维度的采购建议计算逻辑
 */
class PurchaseSuggestionModel
{
    private $dbErp;
    private $dbLogistics;

    /**
     * 产品阶段优先级映射
     * 稳定期 > 衰退期(清货状态) > 成长期 > 新品期
     * 产品阶段：1成长期，2稳定期，3衰退期，4新品期，5清货',
     */
    private static $stagePriorityMap = [
        2 => 1, // 稳定期 - 最高优先级
        3 => 2, // 衰退期(清货状态)
        5 => 2, // 成长期
        1 => 4, // 新品期 - 最低优先级
        4 => 5  // 其他阶段默认为最低优先级
    ];

    public function __construct()
    {
        $this->dbErp = dbErpMysql::getInstance();
        $this->dbLogistics = dbLMysql::getInstance();
    }

    /**
     * 生成指定周期的采购建议
     * 
     * @param string $weekType 'current'|'next'
     * @param string $calculationDate 计算日期 Y-m-d
     * @return array
     */
    public function generateWeeklyPurchaseSuggestion($weekType, $calculationDate = null)
    {
        if (!$calculationDate) {
            $calculationDate = date('Y-m-d');
        }

        try {
            $this->dbLogistics->beginTransaction();

            // 1. 获取周开始日期
            $weekStartDate = $this->getWeekStartDate($calculationDate);

            // 2. 获取所有需要计算的SKU+站点组合
            $skuCountryList = $this->getSkuCountryListForCalculation();
            
            $processedCount = 0;
            $suggestedCount = 0;
            $redundantCount = 0;

            foreach ($skuCountryList as $skuCountry) {
                // 检查是否已标记为已采购或已忽略（本周内不更新）
                if ($this->isSkuCountryLocked($skuCountry['sku'], $skuCountry['country_code'], $weekStartDate)) {
                    continue;
                }

                $result = $this->calculateSingleSkuPurchaseSuggestion(
                    $skuCountry['sku'],
                    $skuCountry['country_code'],
                    $weekType,
                    $calculationDate,
                    $weekStartDate
                );

                if ($result) {
                    $this->savePurchaseSuggestionResult($result);
                    $processedCount++;

                    if ($result['suggestion_action'] === 'suggest') {
                        $suggestedCount++;
                    } elseif ($result['suggestion_action'] === 'redundant') {
                        $redundantCount++;
                    }
                }
            }

            $this->dbLogistics->commit();

            return [
                'success' => true,
                'week_type' => $weekType,
                'calculation_date' => $calculationDate,
                'week_start_date' => $weekStartDate,
                'processed_count' => $processedCount,
                'suggested_count' => $suggestedCount,
                'redundant_count' => $redundantCount
            ];

        } catch (Exception $e) {
            $this->dbLogistics->rollBack();
            throw $e;
        }
    }

    /**
     * 计算单个SKU+站点的采购建议
     */
    public function calculateSingleSkuPurchaseSuggestion($product, $countryList, $weekType, $calculationDate, $weekStartDate)
    {
        $sku = $product['sku'];
        // 1. 检查是否为特殊产品
        $specialConfig = $this->getSpecialProductConfig($sku);
        $total_fba_stock = 0;
        $total_overseas_stock = 0;
        $total_sales_7 = 0;
        $total_sales_14 = 0;
        $total_sales_30 = 0;

        foreach ($countryList as $country) {
            // 2. 获取该SKU在该站点下的所有产品数据
            $skuProductsData = $this->getSkuProductsData($sku, $country);
            if (empty($skuProductsData)) {
                return null;
            }

            // 3. 确定采购配置（特殊产品优先级最高）
            if ($specialConfig) {
                $purchaseConfig = [
                    'min_days' => $specialConfig['special_days'][0],
                    'max_days' => $specialConfig['special_days'][1], // 特殊产品范围较小
                    'is_special' => true,
                    'special_days' => $specialConfig['special_days']
                ];
            } else {
                // 获取最高优先级产品阶段的配置
                $highestPriorityStage = $this->getHighestPriorityStage($skuProductsData);
                $purchaseConfig = $this->getPurchaseConfig($highestPriorityStage, $country);
                $purchaseConfig['is_special'] = false;
                $purchaseConfig['special_days'] = null;
            }

            if (!$purchaseConfig) {
                return null;
            }

            // 4. 汇总SKU数据
            $aggregatedData = $this->aggregateSkuData($skuProductsData);
            $total_fba_stock += $aggregatedData['total_fba_stock'];
            $total_overseas_stock += $aggregatedData['total_overseas_stock'];

            $total_sales_7 += $aggregatedData['sales_data']['sales_7_avg'];
            $total_sales_14 += $aggregatedData['sales_data']['sales_14_avg'];
            $total_sales_30 += $aggregatedData['sales_data']['sales_30_avg'];

            // 5. 计算预估日销量
            $estimatedDailySales = $this->calculateEstimatedDailySales(
                [
                    'sales_7_avg' => $total_sales_7,
                    'sales_14_avg' => $total_sales_14,
                    'sales_30_avg' => $total_sales_30
                ],
                ['sales_7' => '60', 'sales_14' => '20', 'sales_30' => '20'] // 默认权重
            );
            // 6. 计算库存阈值
            $thresholds = $this->calculatePurchaseThresholds($purchaseConfig, $estimatedDailySales);

            // 7. 判断采购状态
            $currentStock = $total_fba_stock + $total_overseas_stock;
            $purchaseStatus = $this->determinePurchaseStatus($currentStock, $thresholds);
        }


        // 8. 计算最终建议数量
        $suggestionQty = 0;
        if ($purchaseStatus['action'] === 'suggest') {
            $targetStock = ($purchaseConfig['min_days']) * $estimatedDailySales;
            $suggestionQty = max(0, $targetStock - $currentStock);
        }

        return [
            'sku' => $sku,
            'country_code' => json_encode($countryList),
            'suggestion_week' => $weekType,
            'product_stage_priority' => $aggregatedData['highest_priority_stage'],
            'is_special_product' => $purchaseConfig['is_special'] ? 1 : 0,
            'special_days' => $purchaseConfig['special_days'],
            'total_fba_stock' => $total_fba_stock,
            'total_overseas_stock' => $aggregatedData['total_overseas_stock'],
            'total_available_stock' => $currentStock,
            'total_sales_7_avg' => $aggregatedData['sales_data']['sales_7_avg'],
            'total_sales_14_avg' => $aggregatedData['sales_data']['sales_14_avg'],
            'total_sales_30_avg' => $aggregatedData['sales_data']['sales_30_avg'],
            'estimated_daily_sales' => $estimatedDailySales,
            'purchase_min_days' => $purchaseConfig['min_days'],
            'purchase_max_days' => $purchaseConfig['max_days'],
            'buffer_days' => $bufferDays,
            'total_need_days' => $purchaseConfig['min_days'] + $bufferDays,
            'min_threshold' => $thresholds['min'],
            'max_threshold' => $thresholds['max'],
            'suggestion_qty' => $suggestionQty,
            'suggestion_action' => $purchaseStatus['action'],
            'suggestion_reason' => $purchaseStatus['reason'],
            'status' => 'pending',
            'calculation_date' => $calculationDate,
            'week_start_date' => $weekStartDate,
        ];
    }

    /**
     * 获取需要计算的SKU+站点组合
     */
    private function getSkuCountryListForCalculation()
    {
        return $this->dbLogistics->table('fba_storage_summary')
            ->field('DISTINCT sku, country_code')
            ->where('(fba_sellable_qty > 0 OR overseas_warehouse_qty > 0) AND sku IS NOT NULL AND sku != \'\'')
            ->order('sku, country_code')
            ->list();
    }

    /**
     * 检查SKU+站点是否被锁定（已采购或已忽略）
     */
    private function isSkuCountryLocked($sku, $country, $weekStartDate)
    {
        $count = $this->dbLogistics->table('purchase_suggestion')
            ->where('sku = :sku AND country_code = :country_code AND week_start_date = :week_start_date', [
                ':sku' => $sku,
                ':country_code' => $country,
                ':week_start_date' => $weekStartDate
            ])
            ->whereIn('status', ['purchased', 'ignored'])
            ->count();

        return $count > 0;
    }

    /**
     * 获取SKU在指定站点下的所有产品数据
     */
    private function getSkuProductsData($sku, $country)
    {
        return $this->dbLogistics->table('fba_storage_summary')
            ->field('asin, sku, country_code, product_stage, fba_sellable_qty')
            ->where('level_type = 2 and sku = :sku AND country_code = :country_code', [':sku' => $sku, ':country_code' => $country])
            ->list();

    }

    /**
     * 获取特殊产品配置
     */
    private function getSpecialProductConfig($sku)
    {
        $result = $this->dbLogistics->table('special_product')
            ->field('special_days')
            ->where('sku = :sku', [':sku' => $sku])
            ->one();

        return $result ? $result : null;
    }

    /**
     * 获取最高优先级产品阶段
     */
    private function getHighestPriorityStage($productsData)
    {
        $highestPriority = 999;
        $highestStage = 1;

        foreach ($productsData as $product) {
            $stage = $product['product_stage'];
            $priority = self::$stagePriorityMap[$stage] ?? 999;
            
            if ($priority < $highestPriority) {
                $highestPriority = $priority;
                $highestStage = $stage;
            }
        }

        return $highestStage;
    }

    /**
     * 获取采购配置
     */
    private function getPurchaseConfig($productStage, $country)
    {
        $configData = $this->dbLogistics->table('config')
            ->field('data')
            ->where('key_name = :key_name', [':key_name' => 'purchase_config'])
            ->one();

        if (!$configData) {
            return null;
        }

        $configData = $configData['data'];

        $configs = json_decode($configData, true);
        foreach ($configs as $config) {
            if ($config['country'] === $country) {
                foreach ($config['rule'] as $rule) {
                    if ($rule['type'] == $productStage) {
                        return [
                            'min_days' => $rule['min_value'],
                            'max_days' => $rule['max_value']
                        ];
                    }
                }
            }
        }

        return null;
    }

    /**
     * 汇总SKU数据
     */
    private function aggregateSkuData($productsData)
    {
        $asinList = [];
        $totalFbaStock = 0;
        $totalOverseasStock = 0;
        $highestPriorityStage = $this->getHighestPriorityStage($productsData);

        foreach ($productsData as $product) {
            $asinList[] = $product['asin'];
            $totalFbaStock += $product['fba_sellable_qty'] ?? 0;
            $totalOverseasStock += $product['overseas_warehouse_qty'] ?? 0;
        }

        // 获取汇总销量数据（这里需要根据实际销量表结构实现）
        $salesData = $this->getAggregatedSalesData($asinList, $productsData[0]['country_code']);

        return [
            'asin_list' => $asinList,
            'total_fba_stock' => $totalFbaStock,
            'total_overseas_stock' => $totalOverseasStock,
            'highest_priority_stage' => $highestPriorityStage,
            'sales_data' => $salesData
        ];
    }

    /**
     * 获取汇总销量数据
     */
    private function getAggregatedSalesData($asinList, $country)
    {
        // 这里需要根据实际的销量数据表结构来实现
        // 暂时返回模拟数据
        return [
            'sales_7_avg' => 15.5,
            'sales_14_avg' => 18.3,
            'sales_30_avg' => 16.8
        ];
    }

    /**
     * 计算预估日销量
     */
    private function calculateEstimatedDailySales($salesData, $salesConfig)
    {
        $sales7Weight = $salesConfig['sales_7'] / 100;
        $sales14Weight = $salesConfig['sales_14'] / 100;
        $sales30Weight = $salesConfig['sales_30'] / 100;

        return $salesData['sales_7_avg'] * $sales7Weight +
               $salesData['sales_14_avg'] * $sales14Weight +
               $salesData['sales_30_avg'] * $sales30Weight;
    }

    /**
     * 计算采购库存阈值
     */
    private function calculatePurchaseThresholds($purchaseConfig, $estimatedDailySales)
    {
        return [
            'min' => ($purchaseConfig['min_days']) * $estimatedDailySales,
            'max' => ($purchaseConfig['max_days']) * $estimatedDailySales
        ];
    }

    /**
     * 判断采购状态
     */
    private function determinePurchaseStatus($currentStock, $thresholds)
    {
        if ($currentStock < $thresholds['min']) {
            return [
                'action' => 'suggest',
                'reason' => '当前库存低于采购最小值，需要采购'
            ];
        } elseif ($currentStock > $thresholds['max']) {
            return [
                'action' => 'redundant',
                'reason' => '当前库存超过采购最大值，采购冗余'
            ];
        } else {
            return [
                'action' => 'normal',
                'reason' => '当前库存在采购范围内'
            ];
        }
    }

    /**
     * 保存采购建议结果
     */
    public function savePurchaseSuggestionResult($result)
    {
        // 这是一个复杂的UPSERT操作，dbLMysql没有直接封装此功能
        $this->dbLogistics->table('purchase_suggestion')->insert($result);
    }

    /**
     * 获取周开始日期（周一）
     */
    private function getWeekStartDate($date)
    {
        $dateObj = new DateTime($date);
        $dayOfWeek = $dateObj->format('N'); // 1=周一, 7=周日

        if ($dayOfWeek == 1) {
            return $date; // 已经是周一
        } else {
            $daysToSubtract = $dayOfWeek - 1;
            $dateObj->sub(new DateInterval("P{$daysToSubtract}D"));
            return $dateObj->format('Y-m-d');
        }
    }

    /**
     * 清理新周数据（每周一执行）
     */
    public function cleanWeeklyData($weekStartDate = null)
    {
        if (!$weekStartDate) {
            $weekStartDate = $this->getWeekStartDate(date('Y-m-d'));
        }

        // 删除上周的数据
        return $this->dbLogistics->table('purchase_suggestion')
            ->where('week_start_date < :week_start_date', [':week_start_date' => $weekStartDate])
            ->delete();
    }

    /**
     * 更新采购状态
     */
    public function updatePurchaseStatus($sku, $country, $status, $operator = '', $remark = '')
    {
        $weekStartDate = $this->getWeekStartDate(date('Y-m-d'));

        $updateData = [
            'status' => $status,
            'status_operator' => $operator,
            'status_remark' => $remark
        ];

        return $this->dbLogistics->table('purchase_suggestion')
            ->where('sku = :sku AND country_code = :country_code AND week_start_date = :week_start_date', [
                ':sku' => $sku,
                ':country_code' => $country,
                ':week_start_date' => $weekStartDate
            ])
            ->update($updateData);
    }

    /**
     * 获取采购建议列表
     */
    public function getPurchaseSuggestionList($filters = [], $page = 1, $pageSize = 100)
    {
        $whereConditions = ["1=1"];
        $params = [];

        // 构建筛选条件
        if (!empty($filters['country_code'])) {
            $whereConditions[] = "country_code = :country_code";
            $params[':country_code'] = $filters['country_code'];
        }

        if (!empty($filters['suggestion_week'])) {
            $whereConditions[] = "suggestion_week = :suggestion_week";
            $params[':suggestion_week'] = $filters['suggestion_week'];
        }

        if (!empty($filters['suggestion_action'])) {
            $whereConditions[] = "suggestion_action = :suggestion_action";
            $params[':suggestion_action'] = $filters['suggestion_action'];
        }

        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['sku'])) {
            $whereConditions[] = "sku LIKE :sku";
            $params[':sku'] = '%' . $filters['sku'] . '%';
        }

        if (!empty($filters['calculation_date'])) {
            $whereConditions[] = "calculation_date = :calculation_date";
            $params[':calculation_date'] = $filters['calculation_date'];
        }

        // 构建查询条件字符串
        $whereClause = implode(' AND ', $whereConditions);

        // 使用dbLMysql的分页功能
        $result = $this->dbLogistics->table('purchase_suggestion')
            ->where($whereClause, $params)
            ->order('suggestion_qty DESC, sku')
            ->pages($page, $pageSize);

        return [
            'total_count' => $result['total'],
            'page' => $result['page'],
            'page_size' => $pageSize,
            'total_pages' => ceil($result['total'] / $pageSize),
            'list' => $result['list']
        ];
    }
}
