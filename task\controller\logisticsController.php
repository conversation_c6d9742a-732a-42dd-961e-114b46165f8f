<?php


namespace task\controller;

use core\lib\db\dbAfMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\fbaStorageSummaryModel;
use plugins\logistics\models\overseasInboundDetailModel;
use plugins\logistics\models\fbaInboundShipmentRawModel;
use plugins\logistics\models\fbaInboundShipmentDetailModel;
use plugins\logistics\models\fbaSalesStatisticsModel;
use plugins\logistics\models\StockSuggestionModel;
use plugins\logistics\models\FbaRedundantModel;
use plugins\logistics\models\PurchaseSuggestionModel;

class logisticsController
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }

    /**
     * FBA库存数据汇总处理
     * 分批从源数据库读取明细数据，关联店铺和listing信息后汇总到目标数据库
     * @return void
     */
    public function getFbaStorageSummary()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $page_size = 100; // 100条每批
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_fba_storage_summary';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 验证输入参数
        $this->validateSummaryParams($syncDate);

        // 实例化汇总模型
        $summaryModel = new fbaStorageSummaryModel();

        // 执行分批循环汇总处理
        $result = $this->processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size);
        $offset += $page_size;

        $endTime = microtime(true);
        $processingTime = round($endTime - $startTime, 2);

        if ($result['success']) {
            // 返回成功结果
            if ($offset >= $result['total']) {
                $redis->del($redis_key);
                SetReturn(2, "FBA库存汇总完成：处理{$result['total']}条", [
                    'sync_date' => $syncDate,
                    'total' => $result['total'],
                    'processing_time' => $processingTime,
                    'start_time' => $result['start_time'],
                    'end_time' => $result['end_time']
                ]);
            } else {
                $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
                returnSuccess([],"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条");
            }
        } else {
            $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total_count']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
            SetReturn(-1, "FBA库存汇总失败：" . $result['error_message'], [
                'sync_date' => $syncDate,
                'error_message' => $result['error_message'],
                'processing_time' => $processingTime
            ]);
        }
    }

    /**
     * 验证汇总参数
     * @param string $syncDate 同步日期
     * @throws \Exception
     */
    private function validateSummaryParams($syncDate)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 分批循环处理汇总逻辑
     * @param fbaStorageSummaryModel $summaryModel 汇总模型实例
     * @param string $syncDate 同步日期
     * @param int $page 当前页码
     * @param int $page_size 每页大小
     * @return array 处理结果
     */
    private function processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size)
    {
        $result = [
            'success' => false,
            'total' => 0,
            'error_message' => '',
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => '',
            'processing_time' => 0
        ];

        try {
            if ($page == 1) {
                // 清理目标表中当天的数据
                // $summaryModel->clearTargetData($syncDate);
            }

            // 获取源数据总数
            $totalCount = $summaryModel->getSourceDataCount($syncDate);
            $result['total'] = $totalCount;

            if ($totalCount == 0) {
                $result['success'] = true;
                $result['end_time'] = date('Y-m-d H:i:s');
                return $result;
            }

            // 分批处理数据
            $processedCount = 0;
            $allSummaryData = [];

            // 读取一批源数据
            $batchData = $summaryModel->getSourceDataBatch($syncDate, $page, $page_size);
            $batchData = $batchData['list'];

            if (empty($batchData)) {
                return $result;
            }

            // 关联店铺和listing数据
            $enrichedData = $this->enrichBatchData($batchData);
            
            // 处理这批数据的四维度汇总
            $batchSummaryData = $summaryModel->processBatchSummary($enrichedData, $syncDate);
            
            // 合并到总汇总数据中
            $this->mergeSummaryData($allSummaryData, $batchSummaryData);

            $processedCount += count($batchData);
            $result['total_processed'] = $processedCount;
            // 批量插入所有汇总数据
            if (!empty($allSummaryData)) {
                $insertResult = $summaryModel->batchInsertSummaryData($allSummaryData);
                $result['total_inserted'] += $insertResult['inserted'];
                $result['total_updated'] += $insertResult['updated'];
            }


            $result['success'] = true;
            $result['end_time'] = date('Y-m-d H:i:s');

        } catch (\Exception $e) {
            $result['error_message'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 关联店铺和listing数据
     * @param array $batchData 批次数据
     * @return array 关联后的数据
     */
    private function enrichBatchData($batchData)
    {
        // 获取所有sid用于查询店铺信息
        $sids = array_unique(array_column($batchData, 'sid'));
        $shopData = $this->getShopDataBySids($sids);

        // 查询所有国家的code
        $countryCodes = dbAfMysql::getInstance()->table('market')->field('code,country')->list();
        $countryCodeMap = array_column($countryCodes, 'code', 'country');

        // 获取所有asin+country组合用于查询listing信息
        $asinCountryPairs = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            if (!empty($countryCode)) {
                $asinCountryPairs[] = [
                    'asin' => $row['asin'],
                    'country_code' => $countryCode
                ];
            }
        }
        $listingData = $this->getListingDataByAsinCountry($asinCountryPairs);

        // 关联数据
        $enrichedData = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            $listingKey = $row['asin'] . '|' . $countryCode;

            $enrichedRow = $row;
            $enrichedRow['country'] = $country;
            $enrichedRow['country_code'] = $countryCode;
            $enrichedRow['product_stage'] = $listingData[$listingKey]['product_stage'] ?? 0;
            $enrichedRow['product_positioning'] = $listingData[$listingKey]['product_positioning'] ?? 0;
            $enrichedRow['stock_positioning'] = $listingData[$listingKey]['stock_positioning'] ?? 0;

            $enrichedData[] = $enrichedRow;
        }

        return $enrichedData;
    }

    /**
     * 更新FBA库存汇总表的销量数据
     * 从利润统计数据计算销量并更新到FBA库存汇总表
     */
    public function updateFbaSalesData()
    {
        // 获取参数
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $batchSize = (int)($_POST['batch_size'] ?? 100);

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            returnError('日期格式错误，请使用YYYY-MM-DD格式');
        }

        try {
            // 引入销量统计模型
            $model = new fbaSalesStatisticsModel();

            // 执行销量数据更新
            $result = $model->updateFbaSalesData($syncDate, $batchSize);


            // 返回结果
            returnSuccess([
                'sync_date' => $syncDate,
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count'],
                'error_list' => $result['error_list']
            ], "销量数据更新完成，成功处理 {$result['success_count']} 条记录");

        } catch (\Exception $e) {
            returnError('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据店铺ID获取店铺数据
     * @param array $sids 店铺ID数组
     * @return array 店铺数据
     */
    private function getShopDataBySids($sids)
    {
        if (empty($sids)) {
            return [];
        }

        $erpDb = dbErpMysql::getInstance();
        $shopList = $erpDb->table('lingxing_shop_list')->whereIn('sid', $sids)->field('sid, country')->list();

        $shopData = array_column($shopList, null, 'sid');

        return $shopData;
    }

    /**
     * 根据ASIN和国家获取listing数据
     * @param array $asinCountryPairs ASIN和国家组合数组
     * @return array listing数据
     */
    private function getListingDataByAsinCountry($asinCountryPairs)
    {
        if (empty($asinCountryPairs)) {
            return [];
        }

        $localDb = dbAfMysql::getInstance();
        $conditions = [];
        $params = [];

        foreach ($asinCountryPairs as $index => $pair) {
            $conditions[] = "(asin = :asin{$index} AND country_code = :country_code{$index})";
            $params["asin{$index}"] = $pair['asin'];
            $params["country_code{$index}"] = $pair['country_code'];
        }

        $whereClause = implode(' OR ', $conditions);
        $sql = "SELECT asin, country_code, product_stage, product_positioning, stock_positioning
                FROM listing_data
                WHERE $whereClause";

        $listingList = $localDb->queryAll($sql, $params);

        $listingData = [];
        foreach ($listingList as $listing) {
            $key = $listing['asin'] . '|' . $listing['country_code'];
            $listingData[$key] = $listing;
        }

        return $listingData;
    }

    /**
     * 合并汇总数据
     * @param array &$allSummaryData 总汇总数据（引用传递）
     * @param array $batchSummaryData 批次汇总数据
     */
    private function mergeSummaryData(&$allSummaryData, $batchSummaryData)
    {
        foreach ($batchSummaryData as $data) {
            $key = $this->generateSummaryKey($data);

            if (isset($allSummaryData[$key])) {
                // 合并数值字段
                $this->mergeNumericFields($allSummaryData[$key], $data);
                // 合并列表字段
                $this->mergeListFields($allSummaryData[$key], $data);
            } else {
                $allSummaryData[$key] = $data;
            }
        }
    }

    /**
     * 生成汇总数据的唯一键
     * @param array $data 汇总数据
     * @return string 唯一键
     */
    private function generateSummaryKey($data)
    {
        $keyParts = [$data['level_type'], $data['asin'], $data['sync_date']];

        switch ($data['level_type']) {
            case 1: // 店铺级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                $keyParts[] = $data['sid'];
                break;
            case 2: // 站点级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                break;
            case 3: // SKU级
                $keyParts[] = $data['sku'];
                break;
            case 4: // ASIN级
                // 只需要基本字段
                break;
        }

        return implode('|', $keyParts);
    }

    /**
     * 合并数值字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeNumericFields(&$target, $source)
    {
        $numericFields = [
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price'
        ];

        foreach ($numericFields as $field) {
            $target[$field] = ($target[$field] ?? 0) + ($source[$field] ?? 0);
        }
    }

    /**
     * 合并列表字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeListFields(&$target, $source)
    {
        $listFields = ['shop_list', 'country_code_list', 'sku_list'];

        foreach ($listFields as $field) {
            $targetList = $target[$field] ?? [];
            $sourceList = $source[$field] ?? [];
            $target[$field] = array_unique(array_merge($targetList, $sourceList));
        }

        // 更新计数
        $target['shop_count'] = count($target['shop_list']);
        $target['site_count'] = count($target['site_list']);
        $target['sku_count'] = count($target['sku_list']);
        $target['updated_at'] = date('Y-m-d H:i:s');
    }

    /**
     * 海外仓备货单SKU拆分处理
     * 循环处理所有备货单数据，每次处理100条
     * @return void
     */
    public function processOverseasInboundSplit()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $batchSize = 100; // 100条每批
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_overseas_inbound_split';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 实例化模型
        $detailModel = new overseasInboundDetailModel();

        // 清理当天的明细数据
        $detailModel->clearDetailsByDate($syncDate);

        $result = $detailModel->getUnprocessedOrders($page, $batchSize);

        $orders = $result['list'];

        if (!empty($orders)) {
            // 处理这批数据
            $batchDetails = [];
            foreach ($orders as $order) {
                try {
                    $skuDetails = $detailModel->splitOrderBySku($order);
                    $batchDetails = array_merge($batchDetails, $skuDetails);
                } catch (\Exception $e) {
                }
            }

            // 批量插入明细数据
            if (!empty($batchDetails)) {
                $insertResult = $detailModel->batchInsertDetails($batchDetails);
                if (!$insertResult) {
                }
            }

            $offset += $batchSize;
            $page++;
        }

        if ($offset >= $result['total']) {
            $redis->del($redis_key);
            SetReturn(2, "海外仓备货单SKU拆分完成：处理{$result['total']}个备货单", [
                'sync_date' => $syncDate,
            ]);
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$result['total'],
                'message'=>"海外仓备货单SKU拆分：已处理{$offset}条，一共{$result['total']}条",
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],"海外仓备货单SKU拆分：已处理{$offset}条，一共{$result['total']}条");
        }

    }

    /**
     * FBA货件数据转换：从ERP数据库转换到Logistics数据库
     * @return void
     */
    public function transformFbaInboundShipmentData()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_l_fba_inbound_shipment_detail';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 实例化模型
        $rawModel = new fbaInboundShipmentRawModel();
        $detailModel = new fbaInboundShipmentDetailModel();

        // 循环处理数据
        $rawDataList = $rawModel->getUnprocessedData($page, 100);

        if (empty($rawDataList) || empty($rawDataList['list'])) {
            SetReturn(2, "FBA货件数据转换完成：无待转换的原始数据", [
                'sync_date' => $syncDate,
            ]);
            return;
        }

        $totalCount = $rawDataList['total'] ?? 0;
        $rawDataList = $rawDataList['list'] ?? [];
        $result = $detailModel->syncFromRawData($rawDataList);
        $offset += 100;

        if ($offset >= $totalCount) {
            $redis->del($redis_key);
            SetReturn(2, "FBA货件数据转换完成：处理{$totalCount}条", [
                'sync_date' => $syncDate,
                'total' => $totalCount,
            ]);
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$totalCount,
                'message'=>"FBA货件数据转换：已处理{$offset}条，一共{$totalCount}条",
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],"FBA货件数据转换：已处理{$offset}条，一共{$totalCount}条");
        }  
    }

    /**
     * 每日备货建议更新
     * 生成本周和下周的备货建议，并更新FBA冗余标记
     * @return void
     */
    public function dailyStockSuggestionUpdate()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

            // 实例化模型
            $stockSuggestionModel = new StockSuggestionModel();
            $fbaRedundantModel = new FbaRedundantModel();

            // 1. 生成本周备货建议
            $currentWeekResult = $stockSuggestionModel->generateWeeklyStockSuggestion('current', $calculationDate);

            // 2. 生成下周备货建议
            $nextWeekResult = $stockSuggestionModel->generateWeeklyStockSuggestion('next', $calculationDate);

            // 3. 更新FBA冗余标记
            $redundantResult = $fbaRedundantModel->updateFbaRedundantStatus($calculationDate);

            // 4. 清理过期数据
            $stockSuggestionModel->cleanExpiredSuggestions(7);

            SetReturn(true, '备货建议更新完成', [
                'calculation_date' => $calculationDate,
                'current_week' => $currentWeekResult,
                'next_week' => $nextWeekResult,
                'redundant_update' => $redundantResult,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '备货建议更新失败: ' . $e->getMessage(), [
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 获取备货建议列表
     * @return void
     */
    public function getStockSuggestionList()
    {
        try {
            $country = $_POST['country'] ?? '';
            $weekType = $_POST['week_type'] ?? 'current'; // current/next
            $suggestionAction = $_POST['suggestion_action'] ?? ''; // suggest/redundant/normal
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 100);
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

            $stockSuggestionModel = new StockSuggestionModel();

            // 构建查询条件
            $whereConditions = ["calculation_date = :calculation_date"];
            $params = [':calculation_date' => $calculationDate];

            if (!empty($country)) {
                $whereConditions[] = "country = :country";
                $params[':country'] = $country;
            }

            if (!empty($weekType)) {
                $whereConditions[] = "suggestion_week = :week_type";
                $params[':week_type'] = $weekType;
            }

            if (!empty($suggestionAction)) {
                $whereConditions[] = "suggestion_action = :suggestion_action";
                $params[':suggestion_action'] = $suggestionAction;
            }

            $whereClause = implode(' AND ', $whereConditions);

            // 查询总数
            $countSql = "SELECT COUNT(*) FROM stock_suggestion WHERE {$whereClause}";
            $totalCount = $stockSuggestionModel->dbLogistics->fetchOne($countSql, $params);

            // 分页查询
            $offset = ($page - 1) * $pageSize;
            $sql = "SELECT * FROM stock_suggestion
                    WHERE {$whereClause}
                    ORDER BY final_suggestion_qty DESC, asin
                    LIMIT {$offset}, {$pageSize}";

            $list = $stockSuggestionModel->dbLogistics->fetchAll($sql, $params);

            SetReturn(true, '获取备货建议列表成功', [
                'total_count' => $totalCount,
                'page' => $page,
                'page_size' => $pageSize,
                'total_pages' => ceil($totalCount / $pageSize),
                'list' => $list
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '获取备货建议列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取FBA冗余库存统计
     * @return void
     */
    public function getFbaRedundantStatistics()
    {
        try {
            $country = $_POST['country'] ?? '';

            $fbaRedundantModel = new FbaRedundantModel();
            $statistics = $fbaRedundantModel->getRedundantStockStatistics($country);

            SetReturn(true, '获取FBA冗余统计成功', [
                'statistics' => $statistics,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '获取FBA冗余统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取FBA冗余库存明细
     * @return void
     */
    public function getFbaRedundantDetails()
    {
        try {
            $filters = [
                'country' => $_POST['country'] ?? '',
                'suggestion_status' => $_POST['suggestion_status'] ?? '',
                'is_redundant' => $_POST['is_redundant'] ?? '',
                'asin' => $_POST['asin'] ?? ''
            ];

            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 100);

            $fbaRedundantModel = new FbaRedundantModel();
            $result = $fbaRedundantModel->getRedundantStockDetails($filters, $page, $pageSize);

            SetReturn(true, '获取FBA冗余明细成功', $result);

        } catch (\Exception $e) {
            SetReturn(false, '获取FBA冗余明细失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动设置冗余状态
     * @return void
     */
    public function setRedundantStatus()
    {
        try {
            $asin = $_POST['asin'] ?? '';
            $country = $_POST['country'] ?? '';
            $isRedundant = (int)($_POST['is_redundant'] ?? 0);
            $reason = $_POST['reason'] ?? '';

            if (empty($asin) || empty($country)) {
                SetReturn(false, '参数错误：ASIN和国家不能为空');
                return;
            }

            $fbaRedundantModel = new FbaRedundantModel();
            $result = $fbaRedundantModel->setRedundantStatus($asin, $country, $isRedundant, $reason);

            if ($result) {
                SetReturn(true, '设置冗余状态成功');
            } else {
                SetReturn(false, '设置冗余状态失败');
            }

        } catch (\Exception $e) {
            SetReturn(false, '设置冗余状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置备货建议忽略状态
     * @return void
     */
    public function setStockSuggestionIgnoreStatus()
    {
        try {
            $asin = $_POST['asin'] ?? '';
            $country = $_POST['country'] ?? '';
            $ignoreStatus = (int)($_POST['ignore_status'] ?? 0);
            $operator = $_POST['operator'] ?? '';
            $remark = $_POST['remark'] ?? '';

            if (empty($asin) || empty($country)) {
                SetReturn(false, '参数错误：ASIN和国家不能为空');
                return;
            }

            $stockSuggestionModel = new StockSuggestionModel();
            $result = $stockSuggestionModel->updateIgnoreStatus($asin, $country, $ignoreStatus, $operator, $remark);

            if ($result) {
                SetReturn(true, $ignoreStatus ? '设置忽略成功' : '取消忽略成功');
            } else {
                SetReturn(false, $ignoreStatus ? '设置忽略失败' : '取消忽略失败');
            }

        } catch (\Exception $e) {
            SetReturn(false, '设置忽略状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 每日采购建议更新
     * 生成本周和下周的采购建议
     * @return void
     */
    public function dailyPurchaseSuggestionUpdate()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

            // 实例化模型
            $purchaseSuggestionModel = new PurchaseSuggestionModel();

            // 检查是否是周一，如果是则清理上周数据
            $dateObj = new \DateTime($calculationDate);
            $dayOfWeek = $dateObj->format('N'); // 1=周一, 7=周日

            if ($dayOfWeek == 1) {
                $weekStartDate = $calculationDate;
                $purchaseSuggestionModel->cleanWeeklyData($weekStartDate);
            }

            // 1. 生成本周采购建议
            $currentWeekResult = $purchaseSuggestionModel->generateWeeklyPurchaseSuggestion('current', $calculationDate);

            // 2. 生成下周采购建议
            $nextWeekResult = $purchaseSuggestionModel->generateWeeklyPurchaseSuggestion('next', $calculationDate);

            SetReturn(true, '采购建议更新完成', [
                'calculation_date' => $calculationDate,
                'current_week' => $currentWeekResult,
                'next_week' => $nextWeekResult,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '采购建议更新失败: ' . $e->getMessage(), [
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 获取采购建议列表
     * @return void
     */
    public function getPurchaseSuggestionList()
    {
        try {
            $country = $_POST['country'] ?? '';
            $weekType = $_POST['week_type'] ?? 'current'; // current/next
            $suggestionAction = $_POST['suggestion_action'] ?? ''; // suggest/redundant/normal
            $status = $_POST['status'] ?? ''; // pending/purchased/ignored
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 100);
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $sku = $_POST['sku'] ?? '';

            $purchaseSuggestionModel = new PurchaseSuggestionModel();

            // 构建筛选条件
            $filters = [
                'country' => $country,
                'suggestion_week' => $weekType,
                'suggestion_action' => $suggestionAction,
                'status' => $status,
                'sku' => $sku,
                'calculation_date' => $calculationDate
            ];

            $result = $purchaseSuggestionModel->getPurchaseSuggestionList($filters, $page, $pageSize);

            SetReturn(true, '获取采购建议列表成功', $result);

        } catch (\Exception $e) {
            SetReturn(false, '获取采购建议列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新采购状态
     * @return void
     */
    public function updatePurchaseStatus()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $country = $_POST['country'] ?? '';
            $status = $_POST['status'] ?? ''; // purchased/ignored/pending
            $operator = $_POST['operator'] ?? '';
            $remark = $_POST['remark'] ?? '';

            if (empty($sku) || empty($country) || empty($status)) {
                SetReturn(false, '参数错误：SKU、国家和状态不能为空');
                return;
            }

            $purchaseSuggestionModel = new PurchaseSuggestionModel();
            $result = $purchaseSuggestionModel->updatePurchaseStatus($sku, $country, $status, $operator, $remark);

            if ($result) {
                SetReturn(true, '更新采购状态成功');
            } else {
                SetReturn(false, '更新采购状态失败');
            }

        } catch (\Exception $e) {
            SetReturn(false, '更新采购状态失败: ' . $e->getMessage());
        }
    }


}
