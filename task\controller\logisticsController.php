<?php


namespace task\controller;

use core\lib\db\dbAfMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\fbaStorageSummaryModel;
use plugins\logistics\models\overseasInboundDetailModel;
use plugins\logistics\models\fbaInboundShipmentRawModel;
use plugins\logistics\models\fbaInboundShipmentDetailModel;
use plugins\logistics\models\fbaSalesStatisticsModel;
use plugins\logistics\models\StockSuggestionModel;
use plugins\logistics\models\FbaRedundantModel;
use plugins\logistics\models\PurchaseSuggestionModel;

require_once dirname(__DIR__) . '/plugins/logistics/models/ShippingSuggestionModel.php';

class logisticsController
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }

    /**
     * FBA库存数据汇总处理
     * 分批从源数据库读取明细数据，关联店铺和listing信息后汇总到目标数据库
     * @return void
     */
    public function getFbaStorageSummary()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $page_size = 100; // 100条每批
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_fba_storage_summary';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 验证输入参数
        $this->validateSummaryParams($syncDate);

        // 实例化汇总模型
        $summaryModel = new fbaStorageSummaryModel();

        // 执行分批循环汇总处理
        $result = $this->processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size);
        $offset += $page_size;

        $endTime = microtime(true);
        $processingTime = round($endTime - $startTime, 2);

        if ($result['success']) {
            // 返回成功结果
            if ($offset >= $result['total']) {
                $redis->del($redis_key);
                SetReturn(2, "FBA库存汇总完成：处理{$result['total']}条", [
                    'sync_date' => $syncDate,
                    'total' => $result['total'],
                    'processing_time' => $processingTime,
                    'start_time' => $result['start_time'],
                    'end_time' => $result['end_time']
                ]);
            } else {
                $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
                returnSuccess([],"FBA库存汇总：已处理{$offset}条，一共{$result['total']}条");
            }
        } else {
            $r_data = [
                    'page'=>$page,
                    'offset'=>$offset,
                    'total'=>$result['total'],
                    'message'=>"FBA库存汇总：已处理{$offset}条，一共{$result['total_count']}条",
                ];
                $redis->set($redis_key,json_encode($r_data));
            SetReturn(-1, "FBA库存汇总失败：" . $result['error_message'], [
                'sync_date' => $syncDate,
                'error_message' => $result['error_message'],
                'processing_time' => $processingTime
            ]);
        }
    }

    /**
     * 验证汇总参数
     * @param string $syncDate 同步日期
     * @throws \Exception
     */
    private function validateSummaryParams($syncDate)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 分批循环处理汇总逻辑
     * @param fbaStorageSummaryModel $summaryModel 汇总模型实例
     * @param string $syncDate 同步日期
     * @param int $page 当前页码
     * @param int $page_size 每页大小
     * @return array 处理结果
     */
    private function processBatchSummaryLoop($summaryModel, $syncDate, $page, $page_size)
    {
        $result = [
            'success' => false,
            'total' => 0,
            'error_message' => '',
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => '',
            'processing_time' => 0
        ];

        try {
            if ($page == 1) {
                // 清理目标表中当天的数据
                // $summaryModel->clearTargetData($syncDate);
            }

            // 获取源数据总数
            $totalCount = $summaryModel->getSourceDataCount($syncDate);
            $result['total'] = $totalCount;

            if ($totalCount == 0) {
                $result['success'] = true;
                $result['end_time'] = date('Y-m-d H:i:s');
                return $result;
            }

            // 分批处理数据
            $processedCount = 0;
            $allSummaryData = [];

            // 读取一批源数据
            $batchData = $summaryModel->getSourceDataBatch($syncDate, $page, $page_size);
            $batchData = $batchData['list'];

            if (empty($batchData)) {
                return $result;
            }

            // 关联店铺和listing数据
            $enrichedData = $this->enrichBatchData($batchData);
            
            // 处理这批数据的四维度汇总
            $batchSummaryData = $summaryModel->processBatchSummary($enrichedData, $syncDate);
            
            // 合并到总汇总数据中
            $this->mergeSummaryData($allSummaryData, $batchSummaryData);

            $processedCount += count($batchData);
            $result['total_processed'] = $processedCount;
            // 批量插入所有汇总数据
            if (!empty($allSummaryData)) {
                $insertResult = $summaryModel->batchInsertSummaryData($allSummaryData);
                $result['total_inserted'] += $insertResult['inserted'];
                $result['total_updated'] += $insertResult['updated'];
            }


            $result['success'] = true;
            $result['end_time'] = date('Y-m-d H:i:s');

        } catch (\Exception $e) {
            $result['error_message'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 关联店铺和listing数据
     * @param array $batchData 批次数据
     * @return array 关联后的数据
     */
    private function enrichBatchData($batchData)
    {
        // 获取所有sid用于查询店铺信息
        $sids = array_unique(array_column($batchData, 'sid'));
        $shopData = $this->getShopDataBySids($sids);

        // 查询所有国家的code
        $countryCodes = dbAfMysql::getInstance()->table('market')->field('code,country')->list();
        $countryCodeMap = array_column($countryCodes, 'code', 'country');

        // 获取所有asin+country组合用于查询listing信息
        $asinCountryPairs = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            if (!empty($countryCode)) {
                $asinCountryPairs[] = [
                    'asin' => $row['asin'],
                    'country_code' => $countryCode
                ];
            }
        }
        $listingData = $this->getListingDataByAsinCountry($asinCountryPairs);

        // 关联数据
        $enrichedData = [];
        foreach ($batchData as $row) {
            $country = $shopData[$row['sid']]['country'] ?? '';
            $countryCode = $countryCodeMap[$country] ?? '';
            $listingKey = $row['asin'] . '|' . $countryCode;

            $enrichedRow = $row;
            $enrichedRow['country'] = $country;
            $enrichedRow['country_code'] = $countryCode;
            $enrichedRow['product_stage'] = $listingData[$listingKey]['product_stage'] ?? 0;
            $enrichedRow['product_positioning'] = $listingData[$listingKey]['product_positioning'] ?? 0;
            $enrichedRow['stock_positioning'] = $listingData[$listingKey]['stock_positioning'] ?? 0;

            $enrichedData[] = $enrichedRow;
        }

        return $enrichedData;
    }

    /**
     * 更新FBA库存汇总表的销量数据
     * 从利润统计数据计算销量并更新到FBA库存汇总表
     */
    public function updateFbaSalesData()
    {
        // 获取参数
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $batchSize = (int)($_POST['batch_size'] ?? 100);

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            returnError('日期格式错误，请使用YYYY-MM-DD格式');
        }

        try {
            // 引入销量统计模型
            $model = new fbaSalesStatisticsModel();

            // 执行销量数据更新
            $result = $model->updateFbaSalesData($syncDate, $batchSize);


            // 返回结果
            returnSuccess([
                'sync_date' => $syncDate,
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count'],
                'error_list' => $result['error_list']
            ], "销量数据更新完成，成功处理 {$result['success_count']} 条记录");

        } catch (\Exception $e) {
            returnError('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据店铺ID获取店铺数据
     * @param array $sids 店铺ID数组
     * @return array 店铺数据
     */
    private function getShopDataBySids($sids)
    {
        if (empty($sids)) {
            return [];
        }

        $erpDb = dbErpMysql::getInstance();
        $shopList = $erpDb->table('lingxing_shop_list')->whereIn('sid', $sids)->field('sid, country')->list();

        $shopData = array_column($shopList, null, 'sid');

        return $shopData;
    }

    /**
     * 根据ASIN和国家获取listing数据
     * @param array $asinCountryPairs ASIN和国家组合数组
     * @return array listing数据
     */
    private function getListingDataByAsinCountry($asinCountryPairs)
    {
        if (empty($asinCountryPairs)) {
            return [];
        }

        $localDb = dbAfMysql::getInstance();
        $conditions = [];
        $params = [];

        foreach ($asinCountryPairs as $index => $pair) {
            $conditions[] = "(asin = :asin{$index} AND country_code = :country_code{$index})";
            $params["asin{$index}"] = $pair['asin'];
            $params["country_code{$index}"] = $pair['country_code'];
        }

        $whereClause = implode(' OR ', $conditions);
        $sql = "SELECT asin, country_code, product_stage, product_positioning, stock_positioning
                FROM listing_data
                WHERE $whereClause";

        $listingList = $localDb->queryAll($sql, $params);

        $listingData = [];
        foreach ($listingList as $listing) {
            $key = $listing['asin'] . '|' . $listing['country_code'];
            $listingData[$key] = $listing;
        }

        return $listingData;
    }

    /**
     * 合并汇总数据
     * @param array &$allSummaryData 总汇总数据（引用传递）
     * @param array $batchSummaryData 批次汇总数据
     */
    private function mergeSummaryData(&$allSummaryData, $batchSummaryData)
    {
        foreach ($batchSummaryData as $data) {
            $key = $this->generateSummaryKey($data);

            if (isset($allSummaryData[$key])) {
                // 合并数值字段
                $this->mergeNumericFields($allSummaryData[$key], $data);
                // 合并列表字段
                $this->mergeListFields($allSummaryData[$key], $data);
            } else {
                $allSummaryData[$key] = $data;
            }
        }
    }

    /**
     * 生成汇总数据的唯一键
     * @param array $data 汇总数据
     * @return string 唯一键
     */
    private function generateSummaryKey($data)
    {
        $keyParts = [$data['level_type'], $data['asin'], $data['sync_date']];

        switch ($data['level_type']) {
            case 1: // 店铺级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                $keyParts[] = $data['sid'];
                break;
            case 2: // 站点级
                $keyParts[] = $data['sku'];
                $keyParts[] = $data['country_code'];
                break;
            case 3: // SKU级
                $keyParts[] = $data['sku'];
                break;
            case 4: // ASIN级
                // 只需要基本字段
                break;
        }

        return implode('|', $keyParts);
    }

    /**
     * 合并数值字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeNumericFields(&$target, $source)
    {
        $numericFields = [
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price'
        ];

        foreach ($numericFields as $field) {
            $target[$field] = ($target[$field] ?? 0) + ($source[$field] ?? 0);
        }
    }

    /**
     * 合并列表字段
     * @param array &$target 目标数据（引用传递）
     * @param array $source 源数据
     */
    private function mergeListFields(&$target, $source)
    {
        $listFields = ['shop_list', 'country_code_list', 'sku_list'];

        foreach ($listFields as $field) {
            $targetList = $target[$field] ?? [];
            $sourceList = $source[$field] ?? [];
            $target[$field] = array_unique(array_merge($targetList, $sourceList));
        }

        // 更新计数
        $target['shop_count'] = count($target['shop_list']);
        $target['site_count'] = count($target['site_list']);
        $target['sku_count'] = count($target['sku_list']);
        $target['updated_at'] = date('Y-m-d H:i:s');
    }

    /**
     * 海外仓备货单SKU拆分处理
     * 循环处理所有备货单数据，每次处理100条
     * @return void
     */
    public function processOverseasInboundSplit()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $batchSize = 100; // 100条每批
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_overseas_inbound_split';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 实例化模型
        $detailModel = new overseasInboundDetailModel();

        // 清理当天的明细数据
        $detailModel->clearDetailsByDate($syncDate);

        $result = $detailModel->getUnprocessedOrders($page, $batchSize);

        $orders = $result['list'];

        if (!empty($orders)) {
            // 处理这批数据
            $batchDetails = [];
            foreach ($orders as $order) {
                try {
                    $skuDetails = $detailModel->splitOrderBySku($order);
                    $batchDetails = array_merge($batchDetails, $skuDetails);
                } catch (\Exception $e) {
                }
            }

            // 批量插入明细数据
            if (!empty($batchDetails)) {
                $insertResult = $detailModel->batchInsertDetails($batchDetails);
                if (!$insertResult) {
                }
            }

            $offset += $batchSize;
            $page++;
        }

        if ($offset >= $result['total']) {
            $redis->del($redis_key);
            SetReturn(2, "海外仓备货单SKU拆分完成：处理{$result['total']}个备货单", [
                'sync_date' => $syncDate,
            ]);
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$result['total'],
                'message'=>"海外仓备货单SKU拆分：已处理{$offset}条，一共{$result['total']}条",
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],"海外仓备货单SKU拆分：已处理{$offset}条，一共{$result['total']}条");
        }

    }

    /**
     * FBA货件数据转换：从ERP数据库转换到Logistics数据库
     * @return void
     */
    public function transformFbaInboundShipmentData()
    {
        $startTime = microtime(true);
        $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_l_fba_inbound_shipment_detail';
        if ($redis->get($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $redis->expire($redis_key,0.5*60*60);
            $page = ($r_data['page']) + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1) * 100;

        // 实例化模型
        $rawModel = new fbaInboundShipmentRawModel();
        $detailModel = new fbaInboundShipmentDetailModel();

        // 循环处理数据
        $rawDataList = $rawModel->getUnprocessedData($page, 100);

        if (empty($rawDataList) || empty($rawDataList['list'])) {
            SetReturn(2, "FBA货件数据转换完成：无待转换的原始数据", [
                'sync_date' => $syncDate,
            ]);
            return;
        }

        $totalCount = $rawDataList['total'] ?? 0;
        $rawDataList = $rawDataList['list'] ?? [];
        $result = $detailModel->syncFromRawData($rawDataList);
        $offset += 100;

        if ($offset >= $totalCount) {
            $redis->del($redis_key);
            SetReturn(2, "FBA货件数据转换完成：处理{$totalCount}条", [
                'sync_date' => $syncDate,
                'total' => $totalCount,
            ]);
        } else {
            $r_data = [
                'page'=>$page,
                'offset'=>$offset,
                'total'=>$totalCount,
                'message'=>"FBA货件数据转换：已处理{$offset}条，一共{$totalCount}条",
            ];
            $redis->set($redis_key,json_encode($r_data));
            returnSuccess([],"FBA货件数据转换：已处理{$offset}条，一共{$totalCount}条");
        }  
    }

    /**
     * 批量备货建议更新
     * 从fba_storage_summary中读取level_type=2的站点层数据，按asin+sku+站点维度处理
     * 每次处理100条，循环页码通过Redis记录
     * @return void
     */
    public function dailyStockSuggestion()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $weekType = $_POST['week_type'] ?? 'current'; // current/next
            $pageSize = 100; // 每批处理100条

            // Redis配置
            $redis = (new \core\lib\predisV())::$client;
            $redisKey = "oa_stock_suggestion_{$weekType}";

            // 获取当前页码
            if ($redis->get($redisKey)) {
                $redisData = json_decode($redis->get($redisKey), true);
                $redis->expire($redisKey, 0.5 * 60 * 60); // 延长过期时间30分钟
                $page = ($redisData['page']) + 1;
            } else {
                $page = 1;
            }

            // 实例化模型
            $stockSuggestionModel = new StockSuggestionModel();

            // 获取站点层数据 (level_type=2)
            $products = $this->getStationLevelProducts($page, $pageSize);

            if (empty($products['list'])) {
                // 没有更多数据，处理完成
                $redis->del($redisKey); // 清除Redis记录
                SetReturn(2, '备货建议处理完成', [
                    'page' => $page,
                    'total_count' => $products['total'],
                    'processed_count' => 0,
                    'suggested_count' => 0,
                    'redundant_count' => 0,
                    'calculation_date' => $calculationDate,
                    'week_type' => $weekType
                ]);
                return;
            }

            // 处理当前批次数据
            $processedCount = 0;
            $suggestedCount = 0;
            $redundantCount = 0;

            foreach ($products['list'] as $product) {
                // 检查是否被忽略，如果被忽略则跳过
                if ($stockSuggestionModel->isProductIgnored($product['id'], $calculationDate)) {
                    continue;
                }

                $result = $stockSuggestionModel->calculateSingleProductSuggestion(
                    $product,
                    $weekType,
                    $calculationDate
                );


                if ($result) {
                    $stockSuggestionModel->saveSuggestionResult($result);
                    $processedCount++;

                    if ($result['suggestion_action'] === 'suggest') {
                        $suggestedCount++;
                    } elseif ($result['suggestion_action'] === 'redundant') {
                        $redundantCount++;
                    }
                }
            }

            // 更新Redis页码
            $redis->setex($redisKey, 0.5 * 60 * 60, json_encode(['page' => $page]));

            // 检查是否还有更多数据
            $hasMore = ($page * $pageSize) < $products['total'];
            $code = $hasMore ? 1 : 2; // 1=继续处理, 2=处理完成

            if (!$hasMore) {
                $redis->del($redisKey); // 清除Redis记录
            }

            SetReturn($code, $hasMore ? '继续处理下一批数据' : '当前批次处理完成', [
                'page' => $page,
                'total_count' => $products['total'],
                'current_batch_size' => count($products['list']),
                'processed_count' => $processedCount,
                'suggested_count' => $suggestedCount,
                'redundant_count' => $redundantCount,
                'calculation_date' => $calculationDate,
                'week_type' => $weekType,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '批量备货建议更新失败: ' . $e->getMessage(), [
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 获取站点层产品数据 (level_type=2)
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    private function getStationLevelProducts($page, $pageSize)
    {
        // 使用ERP数据库查询fba_storage_summary表
        $dbLogistics = dbLMysql::getInstance();

        // 查询站点层数据 (level_type=2)，按asin+sku+站点维度
        $result = $dbLogistics->table('fba_storage_summary')
            ->where('level_type = 2')
            ->order('asin, sku, country_code')
            ->pages($page, $pageSize);

        return $result;
    }

    /**
     * 批量采购建议更新 - 仿照getFbaStorageSummary的循环逻辑
     * 从fba_storage_summary中读取SKU+站点维度数据
     * 每次处理100条，循环页码通过Redis记录
     * @return void
     */
    public function dailyPurchaseSuggestion()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $pageSize = 100; // 每批处理100条

            // Redis配置
            $redis = (new \core\lib\predisV())::$client;
            $redisKey = "oa_purchase_suggestion";

            // 获取当前页码
            if ($redis->get($redisKey)) {
                $redisData = json_decode($redis->get($redisKey), true);
                $redis->expire($redisKey, 0.5 * 60 * 60); // 延长过期时间30分钟
                $page = ($redisData['page']) + 1;
            } else {
                $page = 1;
            }

            // 实例化模型
            $purchaseSuggestionModel = new PurchaseSuggestionModel();
            
            // 获取SKU数据
            $skuList = $this->getSkuListForPurchase($page, $pageSize);

            if (empty($skuList['list'])) {
                // 没有更多数据，处理完成
                $redis->del($redisKey); // 清除Redis记录
                SetReturn(2, '采购建议处理完成', [
                    'page' => $page,
                    'total_count' => $skuList['total'],
                    'processed_count' => 0,
                    'suggested_count' => 0,
                    'redundant_count' => 0,
                    'calculation_date' => $calculationDate
                ]);
                return;
            }

            // 处理当前批次数据
            $processedCount = 0;
            $suggestedCount = 0;
            $redundantCount = 0;

            foreach ($skuList['list'] as $skuCountry) {
                // 查询当前sku下的国家
                $sku = $skuCountry['sku'];
                $skuCountryList = $this->getSkuCountryListForPurchase($sku);
                $result = $purchaseSuggestionModel->calculateSingleSkuPurchaseSuggestion(
                    $skuCountry,
                    $skuCountryList,
                    'current', // week_type
                    $calculationDate,
                    $calculationDate
                );

                if ($result) {
                    $purchaseSuggestionModel->savePurchaseSuggestionResult($result);
                    $processedCount++;

                    if ($result['suggestion_action'] === 'suggest') {
                        $suggestedCount++;
                    } elseif ($result['suggestion_action'] === 'redundant') {
                        $redundantCount++;
                    }
                }
            }

            // 更新Redis页码
            $redis->setex($redisKey, 0.5 * 60 * 60, json_encode(['page' => $page]));

            // 检查是否还有更多数据
            $hasMore = ($page * $pageSize) < $skuList['total'];
            $code = $hasMore ? 1 : 2; // 1=继续处理, 2=处理完成

            if (!$hasMore) {
                $redis->del($redisKey); // 清除Redis记录
            }

            SetReturn($code, $hasMore ? '继续处理下一批数据' : '当前批次处理完成', [
                'page' => $page,
                'total_count' => $skuList['total'],
                'current_batch_size' => count($skuList['list']),
                'processed_count' => $processedCount,
                'suggested_count' => $suggestedCount,
                'redundant_count' => $redundantCount,
                'calculation_date' => $calculationDate,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '批量采购建议更新失败: ' . $e->getMessage(), [
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 获取SKU数据（用于采购建议）
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    private function getSkuListForPurchase($page, $pageSize)
    {
        // 使用ERP数据库查询fba_storage_summary表
        $dbLogistics = dbLMysql::getInstance();

        // 查询SKU+站点组合数据
        $result = $dbLogistics->table('fba_storage_summary')
            ->field('DISTINCT sku')
            ->where('sku IS NOT NULL AND sku != \'\' and country_code IS NOT NULL AND country_code != \'\'')
            ->order('id')
            ->pages($page, $pageSize);

        return $result;
    }

    /**
     * 获取SKU+站点组合数据（用于采购建议）
     * @param string $sku
     * @return array
     */
    private function getSkuCountryListForPurchase($sku)
    {
        // 使用ERP数据库查询fba_storage_summary表
        $dbLogistics = dbLMysql::getInstance();

        // 查询SKU+站点组合数据
        $result = $dbLogistics->table('fba_storage_summary')
            ->field('sku, country_code')
            ->where('sku =:sku and country_code IS NOT NULL AND country_code != \'\'', ['sku' => $sku])
            ->list();

        return $result;
    }

    /**
     * 清理周采购建议数据
     * @return void
     */
    public function cleanWeeklyPurchaseData()
    {
        try {
            $purchaseSuggestionModel = new PurchaseSuggestionModel();
            $result = $purchaseSuggestionModel->cleanWeeklyData();

            SetReturn(true, '周采购建议数据清理成功', [
                'deleted_count' => $result
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '周采购建议数据清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量发货建议生成
     * 从备货建议中读取需要发货的产品，生成发货建议
     * 每次处理100条，循环页码通过Redis记录
     * @return void
     */
    public function dailyShippingSuggestion()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $weekType = $_POST['week_type'] ?? 'current'; // current/next
            $pageSize = 100; // 每批处理100条

            // Redis配置
            $redis = (new \core\lib\predisV())::$client;
            $redisKey = "oa_shipping_suggestion_{$weekType}";

            // 获取当前页码
            if ($redis->get($redisKey)) {
                $redisData = json_decode($redis->get($redisKey), true);
                $redis->expire($redisKey, 0.5 * 60 * 60); // 延长过期时间30分钟
                $page = ($redisData['page']) + 1;
            } else {
                $page = 1;
            }

            // 实例化模型
            $shippingSuggestionModel = new ShippingSuggestionModel();

            // 获取需要发货的产品数据
            $products = $this->getProductsNeedShipping($calculationDate, $weekType, $page, $pageSize);

            if (empty($products['list'])) {
                // 没有更多数据，处理完成
                $redis->del($redisKey); // 清除Redis记录
                SetReturn(2, '发货建议处理完成', [
                    'page' => $page,
                    'total_count' => $products['total'],
                    'processed_count' => 0,
                    'suggested_count' => 0,
                    'calculation_date' => $calculationDate,
                    'week_type' => $weekType
                ]);
                return;
            }

            // 处理当前批次数据
            $processedCount = 0;
            $suggestedCount = 0;
            $errors = [];

            foreach ($products['list'] as $product) {
                try {
                    // 计算单个产品的发货建议
                    $suggestion = $shippingSuggestionModel->calculateSingleProductShipping(
                        $product,
                        $calculationDate,
                        $weekType
                    );

                    if ($suggestion) {
                        // 保存发货建议
                        $shippingSuggestionModel->saveSuggestion($suggestion);
                        $processedCount++;
                        $suggestedCount++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "SKU {$product['sku']} 处理失败: " . $e->getMessage();
                }
            }

            // 更新Redis页码
            $redis->setex($redisKey, 0.5 * 60 * 60, json_encode(['page' => $page]));

            // 检查是否还有更多数据
            $hasMore = ($page * $pageSize) < $products['total'];
            $code = $hasMore ? 1 : 2; // 1=继续处理, 2=处理完成

            if (!$hasMore) {
                $redis->del($redisKey); // 清除Redis记录
            }

            SetReturn($code, $hasMore ? '继续处理下一批数据' : '当前批次处理完成', [
                'page' => $page,
                'total_count' => $products['total'],
                'current_batch_size' => count($products['list']),
                'processed_count' => $processedCount,
                'suggested_count' => $suggestedCount,
                'calculation_date' => $calculationDate,
                'week_type' => $weekType,
                'has_more' => $hasMore,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            SetReturn(false, '批量发货建议生成失败: ' . $e->getMessage(), [
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 获取需要发货的产品数据
     * @param string $calculationDate 计算日期
     * @param string $weekType 周类型
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    private function getProductsNeedShipping($calculationDate, $weekType, $page, $pageSize)
    {
        $dbLogistics = dbLMysql::getInstance();

        // 从备货建议表获取需要发货的产品
        $result = $dbLogistics->table('stock_suggestion')
            ->field('sku, asin, country_code, product_stage, current_fba_stock as fba_sellable_qty,
                     current_overseas_stock as overseas_warehouse_qty, final_suggestion_qty,
                     estimated_daily_sales')
            ->where('calculation_date = :calculation_date AND suggestion_week = :week_type
                     AND final_suggestion_qty > 0 AND suggestion_action = :action', [
                'calculation_date' => $calculationDate,
                'week_type' => $weekType,
                'action' => 'suggest'
            ])
            ->order('final_suggestion_qty DESC, sku')
            ->pages($page, $pageSize);

        return $result;
    }

    /**
     * 获取发货建议列表
     * @return void
     */
    public function getShippingSuggestionList()
    {
        try {
            $filters = [
                'sku' => $_POST['sku'] ?? '',
                'site' => $_POST['site'] ?? '',
                'status' => $_POST['status'] ?? '',
                'suggestion_date' => $_POST['suggestion_date'] ?? '',
                'shipping_method' => $_POST['shipping_method'] ?? '',
                'urgent_flag' => isset($_POST['urgent_flag']) ? (int)$_POST['urgent_flag'] : null
            ];

            $page = max(1, (int)($_POST['page'] ?? 1));
            $pageSize = min(100, max(1, (int)($_POST['page_size'] ?? 20)));

            $shippingSuggestionModel = new ShippingSuggestionModel();
            $result = $shippingSuggestionModel->getShippingSuggestionList($filters, $page, $pageSize);

            SetReturn(true, '获取发货建议列表成功', $result);

        } catch (\Exception $e) {
            SetReturn(false, '获取发货建议列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新发货建议状态
     * @return void
     */
    public function updateShippingSuggestionStatus()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $status = $_POST['status'] ?? '';

            if ($id <= 0) {
                SetReturn(false, '无效的建议ID');
                return;
            }

            $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
            if (!in_array($status, $allowedStatus)) {
                SetReturn(false, '无效的状态值');
                return;
            }

            $shippingSuggestionModel = new ShippingSuggestionModel();
            $result = $shippingSuggestionModel->updateSuggestionStatus($id, $status);

            if ($result) {
                SetReturn(true, '状态更新成功');
            } else {
                SetReturn(false, '状态更新失败');
            }

        } catch (\Exception $e) {
            SetReturn(false, '状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新发货建议状态
     * @return void
     */
    public function batchUpdateShippingSuggestionStatus()
    {
        try {
            $ids = $_POST['ids'] ?? [];
            $status = $_POST['status'] ?? '';

            if (empty($ids) || !is_array($ids)) {
                SetReturn(false, '请选择要更新的建议');
                return;
            }

            $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
            if (!in_array($status, $allowedStatus)) {
                SetReturn(false, '无效的状态值');
                return;
            }

            $shippingSuggestionModel = new ShippingSuggestionModel();
            $result = $shippingSuggestionModel->batchUpdateStatus($ids, $status);

            if ($result) {
                SetReturn(true, '批量状态更新成功', ['updated_count' => count($ids)]);
            } else {
                SetReturn(false, '批量状态更新失败');
            }

        } catch (\Exception $e) {
            SetReturn(false, '批量状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取发货建议详情
     * @return void
     */
    public function getShippingSuggestionDetail()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);

            if ($id <= 0) {
                SetReturn(false, '无效的建议ID');
                return;
            }

            $shippingSuggestionModel = new ShippingSuggestionModel();
            $result = $shippingSuggestionModel->getSuggestionDetail($id);

            if ($result) {
                SetReturn(true, '获取详情成功', $result);
            } else {
                SetReturn(false, '建议不存在');
            }

        } catch (\Exception $e) {
            SetReturn(false, '获取详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取发货建议统计
     * @return void
     */
    public function getShippingSuggestionStatistics()
    {
        try {
            $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $_POST['end_date'] ?? date('Y-m-d');

            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) ||
                !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
                SetReturn(false, '日期格式错误，应为 YYYY-MM-DD 格式');
                return;
            }

            $shippingSuggestionModel = new ShippingSuggestionModel();
            $result = $shippingSuggestionModel->getSuggestionStatistics($startDate, $endDate);

            SetReturn(true, '获取统计数据成功', $result);

        } catch (\Exception $e) {
            SetReturn(false, '获取统计数据失败: ' . $e->getMessage());
        }
    }


}
