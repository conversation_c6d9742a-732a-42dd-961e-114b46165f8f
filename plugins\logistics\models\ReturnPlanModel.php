<?php
namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\ExceptionError;
use plugins\logistics\form\ApprovalForm;
use plugins\logistics\models\userModel;

class ReturnPlanModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }
    
    /**
     * 批量创建回货计划
     * @param array $planData 计划数据
     * @return array
     */
    public function batchCreate(array $planData): array
    {
        $this->db->beginTransaction();
        
        try {
            $planIds = [];
            $planNo = $this->generatePlanNo();
            
            foreach ($planData as $item) {
                $data = [
                    'plan_no' => $planNo,
                    'sku' => $item['sku'],
                    'site' => $item['site'],
                    'week_period' => $item['week_period'],
                    'demand_qty' => $item['demand_qty'],
                    'status' => 'draft',
                    'creator_id' => userModel::$qwuser_id,
                    'creator_name' => userModel::$wname,
                    'remark' => $item['remark'] ?? ''
                ];
                
                $planId = $this->db->table('return_plan')->insert($data);
                $planIds[] = $planId;
            }
            
            $this->db->commit();
            return $planIds;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("创建回货计划失败: " . $e->getMessage());
        }
    }
    
    /**
     * 提交审批
     * @param string $planNo 计划单号
     * @return bool
     */
    public function submitApproval(string $planNo): bool
    {
        $this->db->beginTransaction();
        
        try {
            // 获取计划数据
            $plans = $this->db->table('return_plan')
                ->where('plan_no = :plan_no', ['plan_no' => $planNo])
                ->all();
            
            if (empty($plans)) {
                throw new ExceptionError("计划不存在");
            }
            
            // 检查状态
            foreach ($plans as $plan) {
                if ($plan['status'] !== 'draft') {
                    throw new ExceptionError("只有草稿状态的计划才能提交审批");
                }
            }
            
            // 启动审批流程
            $title = "回货计划审批 - {$planNo}";
            $businessData = [
                'plan_no' => $planNo,
                'plan_count' => count($plans),
                'total_demand_qty' => array_sum(array_column($plans, 'demand_qty'))
            ];
            
            $instanceId = ApprovalForm::startApproval('return_plan', $plans[0]['id'], $title, $businessData);
            
            // 更新计划状态
            $this->db->table('return_plan')
                ->where('plan_no = :plan_no', ['plan_no' => $planNo])
                ->update([
                    'status' => 'pending',
                    'approval_instance_id' => $instanceId
                ]);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("提交审批失败: " . $e->getMessage());
        }
    }
    
    /**
     * 审批通过后处理
     * @param string $planNo 计划单号
     * @return bool
     */
    public function approvalPassed(string $planNo): bool
    {
        $this->db->beginTransaction();
        
        try {
            // 更新计划状态
            $this->db->table('return_plan')
                ->where('plan_no = :plan_no', ['plan_no' => $planNo])
                ->update(['status' => 'approved']);
            
            // 生成回货单
            $this->generateReturnOrders($planNo);
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("审批通过处理失败: " . $e->getMessage());
        }
    }
    
    /**
     * 生成回货单
     * @param string $planNo 计划单号
     * @return void
     */
    private function generateReturnOrders(string $planNo): void
    {
        // 获取计划数据
        $plans = $this->db->table('return_plan')
            ->where('plan_no = :plan_no AND status = :status', 
                ['plan_no' => $planNo, 'status' => 'approved'])
            ->all();
        
        // 按SKU+周期分组
        $groupedPlans = [];
        foreach ($plans as $plan) {
            $key = $plan['sku'] . '_' . $plan['week_period'];
            if (!isset($groupedPlans[$key])) {
                $groupedPlans[$key] = [
                    'sku' => $plan['sku'],
                    'week_period' => $plan['week_period'],
                    'plans' => []
                ];
            }
            $groupedPlans[$key]['plans'][] = $plan;
        }
        
        // 为每个SKU+周期组合创建回货单
        foreach ($groupedPlans as $group) {
            $this->createOrUpdateReturnOrder($group);
        }
    }
    
    /**
     * 创建或更新回货单
     * @param array $group 分组数据
     * @return void
     */
    private function createOrUpdateReturnOrder(array $group): void
    {
        $sku = $group['sku'];
        $weekPeriod = $group['week_period'];
        $plans = $group['plans'];
        
        // 检查是否已存在回货单
        $existingOrder = $this->db->table('return_order')
            ->where('sku = :sku AND week_period = :week_period', 
                ['sku' => $sku, 'week_period' => $weekPeriod])
            ->one();
        
        // 计算汇总数据
        $totalDemandQty = array_sum(array_column($plans, 'demand_qty'));
        $calculationResult = $this->calculateReturnOrderData($sku, $weekPeriod, $totalDemandQty);
        
        if ($existingOrder) {
            // 更新现有回货单
            $this->db->table('return_order')
                ->where('id = :id', ['id' => $existingOrder['id']])
                ->update([
                    'total_demand_qty' => $calculationResult['total_demand_qty'],
                    'total_redundant_qty' => $calculationResult['total_redundant_qty'],
                    'deduct_redundant_qty' => $calculationResult['deduct_redundant_qty'],
                    'actual_demand_qty' => $calculationResult['actual_demand_qty']
                ]);
            
            $orderId = $existingOrder['id'];
        } else {
            // 创建新回货单
            $orderData = array_merge([
                'order_no' => $this->generateOrderNo(),
                'sku' => $sku,
                'week_period' => $weekPeriod
            ], $calculationResult);
            
            $orderId = $this->db->table('return_order')->insert($orderData);
        }
        
        // 更新计划表中的回货单关联
        foreach ($plans as $plan) {
            $this->db->table('return_plan')
                ->where('id = :id', ['id' => $plan['id']])
                ->update(['return_order_id' => $orderId]);
        }
    }
    
    /**
     * 计算回货单数据
     * @param string $sku SKU
     * @param string $weekPeriod 周期
     * @param int $totalDemandQty 总需求量
     * @return array
     */
    private function calculateReturnOrderData(string $sku, string $weekPeriod, int $totalDemandQty): array
    {
        // 获取未交冗余（从采购建议计算）
        $totalRedundantQty = $this->getRedundantQtyFromPurchaseSuggestion($sku, $weekPeriod);
        
        // 计算扣减冗余 = min(剩余冗余合计, 需求合计)
        $deductRedundantQty = min($totalRedundantQty, $totalDemandQty);
        
        // 计算实际需求量 = max((需求合计 - 扣减冗余), 0)
        $actualDemandQty = max(($totalDemandQty - $deductRedundantQty), 0);
        
        return [
            'total_demand_qty' => $totalDemandQty,
            'total_redundant_qty' => $totalRedundantQty,
            'deduct_redundant_qty' => $deductRedundantQty,
            'actual_demand_qty' => $actualDemandQty
        ];
    }
    
    /**
     * 从采购建议获取冗余数量
     * @param string $sku SKU
     * @param string $weekPeriod 周期
     * @return int
     */
    private function getRedundantQtyFromPurchaseSuggestion(string $sku, string $weekPeriod): int
    {
        try {
            // 查询采购建议表获取各站点的冗余数据
            $purchaseSuggestions = $this->db->table('purchase_suggestion')
                ->where('sku = :sku', ['sku' => $sku])
                ->all();
            
            $totalRedundant = 0;
            foreach ($purchaseSuggestions as $suggestion) {
                // 计算未交冗余 = 已采购数量 - 已交付数量 - 当前需求
                $purchased = $suggestion['purchased_qty'] ?? 0;
                $delivered = $suggestion['delivered_qty'] ?? 0;
                $currentDemand = $suggestion['current_demand_qty'] ?? 0;
                
                $redundant = $purchased - $delivered - $currentDemand;
                if ($redundant > 0) {
                    $totalRedundant += $redundant;
                }
            }
            
            return $totalRedundant;
        } catch (\Exception $e) {
            // 如果查询失败，返回0
            return 0;
        }
    }
    
    /**
     * 生成计划单号
     * @return string
     */
    private function generatePlanNo(): string
    {
        return 'RP' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 生成回货单号
     * @return string
     */
    private function generateOrderNo(): string
    {
        return 'RO' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 获取计划列表
     * @param array $params 查询参数
     * @return array
     */
    public function getList(array $params = []): array
    {
        $where = '1=1';
        $binds = [];
        
        if (!empty($params['plan_no'])) {
            $where .= ' AND plan_no LIKE :plan_no';
            $binds['plan_no'] = '%' . $params['plan_no'] . '%';
        }
        
        if (!empty($params['sku'])) {
            $where .= ' AND sku LIKE :sku';
            $binds['sku'] = '%' . $params['sku'] . '%';
        }
        
        if (!empty($params['site'])) {
            $where .= ' AND site = :site';
            $binds['site'] = $params['site'];
        }
        
        if (!empty($params['week_period'])) {
            $where .= ' AND week_period = :week_period';
            $binds['week_period'] = $params['week_period'];
        }
        
        if (!empty($params['status'])) {
            $where .= ' AND status = :status';
            $binds['status'] = $params['status'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $offset = ($page - 1) * $pageSize;
        
        $total = $this->db->table('return_plan')
            ->where($where, $binds)
            ->count();
        
        $list = $this->db->table('return_plan')
            ->where($where, $binds)
            ->order('id DESC')
            ->limit($offset, $pageSize)
            ->all();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 根据ID获取计划
     * @param int $id 计划ID
     * @return array|null
     */
    public function getById(int $id): ?array
    {
        return $this->db->table('return_plan')
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    /**
     * 根据计划单号获取计划
     * @param string $planNo 计划单号
     * @return array|null
     */
    public function getByPlanNo(string $planNo): ?array
    {
        return $this->db->table('return_plan')
            ->where('plan_no = :plan_no', ['plan_no' => $planNo])
            ->one();
    }

    /**
     * 更新计划
     * @param int $id 计划ID
     * @param array $data 更新数据
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        return $this->db->table('return_plan')
            ->where('id = :id', ['id' => $id])
            ->update($data);
    }

    /**
     * 删除计划
     * @param int $id 计划ID
     * @return bool
     */
    public function delete(int $id): bool
    {
        return $this->db->table('return_plan')
            ->where('id = :id', ['id' => $id])
            ->delete();
    }

    /**
     * 作废计划
     * @param string $planNo 计划单号
     * @return bool
     */
    public function cancel(string $planNo): bool
    {
        return $this->db->table('return_plan')
            ->where('plan_no = :plan_no', ['plan_no' => $planNo])
            ->update(['status' => 'cancelled']);
    }

    /**
     * 更新状态
     * @param int $id 计划ID
     * @param string $status 状态
     * @return bool
     */
    public function updateStatus(int $id, string $status): bool
    {
        return $this->db->table('return_plan')
            ->where('id = :id', ['id' => $id])
            ->update(['status' => $status]);
    }
}
