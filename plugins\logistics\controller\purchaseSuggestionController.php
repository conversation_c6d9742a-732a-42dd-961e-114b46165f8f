<?php

namespace plugins\logistics\controller;

use plugins\logistics\models\purchaseSuggestionModel;
use plugins\logistics\controller\baseController;

/**
 * 采购建议控制器
 * 处理采购建议的列表查询、状态变更等功能
 */
class purchaseSuggestionController extends baseController
{
    /**
     * 获取采购建议列表
     * @return void
     */
    public function getList()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $site = $_POST['site'] ?? '';
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $priority = $_POST['priority'] ?? ''; // high/medium/low
            $status = $_POST['status'] ?? ''; // pending/purchased/ignored
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 20);

            $filters = [
                'sku' => $sku,
                'site' => $site,
                'calculation_date' => $calculationDate,
                'priority' => $priority,
                'status' => $status
            ];

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->getPurchaseSuggestionList($filters, $page, $pageSize);

            returnSuccess($result, '获取成功');
        } catch (\Exception $e) {
            returnError('获取采购建议列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取采购建议详情
     * @return void
     */
    public function getDetail()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);

            if (!$id) {
                returnError('ID参数不能为空');
            }

            $purchaseModel = new purchaseSuggestionModel();
            $detail = $purchaseModel->getSuggestionDetail($id);

            if (!$detail) {
                returnError('采购建议不存在');
            }

            returnSuccess($detail, '获取成功');
        } catch (\Exception $e) {
            returnError('获取采购建议详情失败: ' . $e->getMessage());
        }
    }

    /**
     * @return void
     */
    public function setIgnoreStatus()
    {
        $id = $_POST['id'] ?? '';
        $ignoreStatus = (int)($_POST['ignore_status'] ?? 0);

        $purchaseSuggestionModel = new purchaseSuggestionModel();
        $db = $purchaseSuggestionModel->dbLogistics;
        $detail = $db->table('purchase_suggestion')->where('id = :id', [':id' => $id])->one();
        if (!$detail) {
            returnError('采购建议不存在');
        }
        if (!in_array($ignoreStatus, [
            $purchaseSuggestionModel::IGNORE_STATUS_WAITING,
            $purchaseSuggestionModel::IGNORE_STATUS_FINISHED,
            $purchaseSuggestionModel::IGNORE_STATUS_IGNORED
        ])) {
            returnError('忽略状态参数错误');
        }
        if ($ignoreStatus == $purchaseSuggestionModel::IGNORE_STATUS_FINISHED && $detail['ignore_status'] == $purchaseSuggestionModel::IGNORE_STATUS_IGNORED) {
            returnError('该采购建议已被忽略，无法设置为已采购状态');
        }
        if ($ignoreStatus == $purchaseSuggestionModel::IGNORE_STATUS_IGNORED && $detail['ignore_status'] == $purchaseSuggestionModel::IGNORE_STATUS_FINISHED) {
            returnError('该采购建议已被设置为已采购状态，无法设置为忽略状态');
        }

        $result = $purchaseSuggestionModel->updateIgnoreStatus($id, $ignoreStatus);

        returnSuccess([], '设置成功');
    }

    /**
     * 批量更新采购建议状态
     * @return void
     */
    public function batchUpdateStatus()
    {
        try {
            $ids = $_POST['ids'] ?? [];
            $status = $_POST['status'] ?? '';

            if (empty($ids) || !is_array($ids)) {
                returnError('ID列表不能为空');
            }

            $allowedStatus = ['pending', 'purchased', 'ignored'];
            if (!in_array($status, $allowedStatus)) {
                returnError('状态参数错误，允许的状态：' . implode(', ', $allowedStatus));
            }

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->batchUpdateStatus($ids, $status);

            if ($result) {
                returnSuccess([], '批量状态更新成功');
            } else {
                returnError('批量状态更新失败');
            }
        } catch (\Exception $e) {
            returnError('批量更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取采购建议统计
     * @return void
     */
    public function getStatistics()
    {
        try {
            $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $_POST['end_date'] ?? date('Y-m-d');

            $purchaseModel = new purchaseSuggestionModel();
            $statistics = $purchaseModel->getPurchaseStatistics($startDate, $endDate);

            returnSuccess($statistics, '获取统计成功');
        } catch (\Exception $e) {
            returnError('获取统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取SKU采购趋势
     * @return void
     */
    public function getSkuTrend()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $site = $_POST['site'] ?? '';
            $days = (int)($_POST['days'] ?? 30);

            if (!$sku || !$site) {
                returnError('SKU和站点参数不能为空');
            }

            $purchaseModel = new purchaseSuggestionModel();
            $trend = $purchaseModel->getSkuPurchaseTrend($sku, $site, $days);

            returnSuccess($trend, '获取趋势成功');
        } catch (\Exception $e) {
            returnError('获取趋势失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成采购建议
     * @return void
     */
    public function generateSuggestion()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->generatePurchaseSuggestion($calculationDate);

            if ($result['success']) {
                returnSuccess($result, '采购建议生成成功');
            } else {
                returnError($result['message'] ?? '采购建议生成失败');
            }
        } catch (\Exception $e) {
            returnError('生成采购建议失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取高优先级采购建议
     * @return void
     */
    public function getHighPriorityList()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $page = (int)($_POST['page'] ?? 1);
            $pageSize = (int)($_POST['page_size'] ?? 20);

            $filters = [
                'calculation_date' => $calculationDate,
                'priority' => 'high',
                'status' => 'pending'
            ];

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->getPurchaseSuggestionList($filters, $page, $pageSize);

            returnSuccess($result, '获取高优先级采购建议成功');
        } catch (\Exception $e) {
            returnError('获取高优先级采购建议失败: ' . $e->getMessage());
        }
    }

    /**
     * 重新计算采购建议
     * @return void
     */
    public function recalculate()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $site = $_POST['site'] ?? '';
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

            if (!$sku || !$site) {
                returnError('SKU和站点参数不能为空');
            }

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->recalculateSingleProduct($sku, $site, $calculationDate);

            if ($result['success']) {
                returnSuccess($result['data'], '重新计算成功');
            } else {
                returnError($result['message'] ?? '重新计算失败');
            }
        } catch (\Exception $e) {
            returnError('重新计算失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取特殊产品配置
     * @return void
     */
    public function getSpecialConfig()
    {
        try {
            $sku = $_POST['sku'] ?? '';
            $site = $_POST['site'] ?? '';

            if (!$sku || !$site) {
                returnError('SKU和站点参数不能为空');
            }

            $purchaseModel = new purchaseSuggestionModel();
            $config = $purchaseModel->getSpecialProductConfig($sku, $site);

            returnSuccess($config ?: [], '获取特殊配置成功');
        } catch (\Exception $e) {
            returnError('获取特殊配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理过期采购建议
     * @return void
     */
    public function cleanExpired()
    {
        try {
            $days = (int)($_POST['days'] ?? 30);

            $purchaseModel = new purchaseSuggestionModel();
            $deletedCount = $purchaseModel->cleanExpiredSuggestions($days);

            returnSuccess([
                'deleted_count' => $deletedCount
            ], "清理完成，删除了 {$deletedCount} 条过期记录");
        } catch (\Exception $e) {
            returnError('清理过期记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出采购建议
     * @return void
     */
    public function export()
    {
        try {
            $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');
            $status = $_POST['status'] ?? '';
            $priority = $_POST['priority'] ?? '';

            $filters = [
                'calculation_date' => $calculationDate,
                'status' => $status,
                'priority' => $priority
            ];

            $purchaseModel = new purchaseSuggestionModel();
            $result = $purchaseModel->getPurchaseSuggestionList($filters, 1, 10000); // 导出所有数据

            if (empty($result['list'])) {
                returnError('没有数据可导出');
            }

            // 设置CSV头部
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="purchase_suggestion_' . $calculationDate . '.csv"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');

            // 输出BOM以支持中文
            echo "\xEF\xBB\xBF";

            $output = fopen('php://output', 'w');

            // CSV头部
            $headers = [
                'SKU', '站点', '产品名称', '产品阶段', '当前FBA库存', '当前海外仓库存',
                '总当前库存', '在途库存', '建议库存', '采购数量', '冗余数量', '优先级',
                '状态', '预估日销量', '安全库存天数', '计算日期'
            ];
            fputcsv($output, $headers);

            // 输出数据
            foreach ($result['list'] as $item) {
                $row = [
                    $item['sku'],
                    $item['site'],
                    $item['product_name'],
                    $item['product_stage'],
                    $item['current_fba_stock'],
                    $item['current_overseas_stock'],
                    $item['total_current_stock'],
                    $item['in_transit_stock'],
                    $item['suggested_stock'],
                    $item['purchase_qty'],
                    $item['redundant_qty'],
                    $item['priority'],
                    $item['status'],
                    $item['estimated_daily_sales'],
                    $item['safety_stock_days'],
                    $item['calculation_date']
                ];
                fputcsv($output, $row);
            }

            fclose($output);
            exit;
        } catch (\Exception $e) {
            returnError('导出失败: ' . $e->getMessage());
        }
    }
}
