<?php

namespace plugins\logistics\controller;

use core\lib\config;

class stockStatisticController extends baseController
{
    // 看板
    public function getResult()
    {
        $data = [
            // 总览
            'inventory_overview'               => [
                'total_qty'            => 152654,
                'available_qty'        => 92654,
                'fba_sellable_qty'     => 42654,
                'purchase_pending_qty' => 2654,
            ],
            // 库存分布
            'inventory_distribution'           => [

                [
                    'month'  => '2024-05',
                    'fba_warehouse'    => 20000,
                    'overseas_warehouse' => 16000,
                    'platform_warehouse' => 11000,
                    'domestic_warehouse' => 8000,
                    'remove_warehouse' => 8000,
                    'total' => 63000
                ],
                [
                    'month'  => '2024-06',
                    'fba_warehouse'    => 20000,
                    'overseas_warehouse' => 16000,
                    'platform_warehouse' => 11000,
                    'domestic_warehouse' => 8000,
                    'remove_warehouse' => 8000,
                    'total' => 63000
                ],
                [
                    'month'  => '2024-07',
                    'fba_warehouse'    => 20000,
                    'overseas_warehouse' => 16000,
                    'platform_warehouse' => 11000,
                    'domestic_warehouse' => 8000,
                    'remove_warehouse' => 8000,
                    'total' => 63000
                ],
            ],
            // 品类分布
            'category_distribution'            => [
                ['category' => 'Q1分类', 'value' => 7000],
                ['category' => 'Q2分类', 'value' => 6900],
                ['category' => 'Q3分类', 'value' => 6200],
                ['category' => 'Q4分类', 'value' => 5800],
                // ...
            ],
            // 产品阶段分布
            'product_stage_distribution'   => [
                ['product_stage' => '1', 'count' => 23000],
                ['product_stage' => '2', 'count' => 25000],
                ['product_stage' => '3', 'count' => 10000],
                ['product_stage' => '4', 'count' => 2300],
                ['product_stage' => '5', 'count' => 2300],
            ],
            // 销售和发货数据
            'sales_and_shipped'                => [
                ['month' => '2024-05', 'sales_qty' => 1800, 'shipped_qty' => 150, 'sales_qty_QoQ' => 123.22, 'shipped_qty_QoQ' => 100.52],
                ['month' => '2024-06', 'sales_qty' => 1900, 'shipped_qty' => 150, 'sales_qty_QoQ' => 111.11, 'shipped_qty_QoQ' => 231.55],
                ['month' => '2024-07', 'sales_qty' => 2000, 'shipped_qty' => 150, 'sales_qty_QoQ' => 105.26, 'shipped_qty_QoQ' => 46.31],
            ],
            // 物流分布
            'logistics_distribution'           => [
                ['month' => '2024-05', 'type_1' => 1800, 'type_2' => 150, 'total' => 2300],
                ['month' => '2024-06', 'type_1' => 1800, 'type_2' => 150, 'total' => 2300],
                ['month' => '2024-07', 'type_1' => 1800, 'type_2' => 150, 'total' => 2300],
            ],
            // 店铺状态分布
            "shop_analysis"                        => [
                "total_num"  => 100,
                "normal_num" => 100,
                "risk_num"   => 100,
                "total_inventory"  => 100,
                "normal_inventory" => 100,
                "risk_inventory"   => 100,
                "total_value"  => 100,
                "normal_value" => 100,
                "risk_value"   => 100,

            ],
            // 库存状态分布
            'inventory_status_distribution'    => [
                'available_inventory'    => [
                    'normal'   => 8000,
                    'warning' => 2000,
                    'out_of_stock'     => 600,
                    'redundant'     => 600,
                    'total'     => 11200,
                ],
                'fba_sellable_qty' => [
                    'normal'   => 8000,
                    'warning' => 2000,
                    'out_of_stock'     => 600,
                    'redundant'     => 600,
                    'total'     => 11200,
                ],
            ],
            // 断货分析
            'out_of_stock_analysis'                => [
                ['month' => '2024-05', 'out_of_stock_qty' => 120, 'out_of_stock_day' => 10, 'out_of_stock_value' => 5000],
                ['month' => '2024-06', 'out_of_stock_qty' => 130, 'out_of_stock_day' => 12, 'out_of_stock_value' => 6000],
                ['month' => '2024-07', 'out_of_stock_qty' => 140, 'out_of_stock_day' => 15, 'out_of_stock_value' => 7000],
            ],
            // 店铺分布
            'shop_distribution'               => [
                ['name' => 'main_backup', 'count' => 25],
                ['name' => 'secondary_backup', 'count' => 30],
                ['name' => 'fk_review', 'count' => 20],
                ['name' => 'brush', 'count' => 25],
            ],
            // 可用库存分布
            'available_inventory_distribution' => [
                'main_backup' => 50000,      // 主备货号 > $50,000
                'secondary_backup' => 30000,  // 次备货号 > $30,000
                'fk_review' => 20000,        // FK回评号 > $20,000
                'brush' => 20000,
                'total' => 120000,
            ],
        ];

        returnSuccess($data);

    }

    // 周转天数统计
    public function getCountryInventoryTurnoverDaysTrend() {
        $country = config::get('country','data_logistics');
        $data = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-$i month", time()));

            $country_map = [];
            $total = 0;
            foreach ($country as $item) {
                $country_map[$item['name']] = rand(70, 110); // 模拟数据
                $total += $country_map[$item['name']];
            }
            $data[] = array_merge([
                'month' => $month,
                'total' => $total
            ], $country_map);
        }

        returnSuccess($data);

    }

    // 按国家
    public function getInventoryTurnoverDaysByCountry() {
        $country = config::get('country','data_logistics');
        $data = [];
        foreach ($country as $item) {
            $data['inventory_distribution'][$item['name']] = [
                'fba_end_count' => rand(1000, 5000), // 模拟数据
                'fba_end_on_way_count' => rand(1000, 5000), // 模拟数据
                'overseas_end_count' => rand(1000, 5000), // 模拟数据
                'sales_count' => rand(1000, 5000), // 模拟数据
                'turnover_days' => rand(70, 110)
            ]; // 模拟数据
            $data['list'][$item['name']] = [
                'fba_end_count' => rand(1000, 5000), // 模拟数据
                'fba_end_amount' => rand(1000, 5000), // 模拟数据
                'fba_end_on_way_count' => rand(1000, 5000), // 模拟数据
                'fba_end_on_way_amount' => rand(1000, 5000), // 模拟数据
                'overseas_end_count' => rand(1000, 5000), // 模拟数据
                'overseas_end_amount' => rand(1000, 5000), // 模拟数据
                'overseas_end_on_way_count' => rand(1000, 5000), // 模拟数据
                'overseas_end_on_way_amount' => rand(1000, 5000), // 模拟数据
                'total_count' => rand(1000, 5000), // 模拟数据
                'total_amount' => rand(1000, 5000), // 模拟数据
                'sales_count' => rand(1000, 5000), // 模拟数据
                'sales_amount' => rand(1000, 5000), // 模拟数据
                'turnover_days' => rand(70, 110)
            ];
        }

        returnSuccess($data);

    }

    // 按运营
    public function getInventoryTurnoverDaysByOp() {
        $data = [];
        for ($i = 0; $i < 10; $i++) {
            $data['dep_distribution']['美国'.$i.'组'] = [
                'min_value' => rand(70, 110), // 模拟数据
                'max_value' => rand(110, 150), // 模拟数据
                // 第一分位、中位、第三分位
                'q1_value' => rand(70, 90),
                'q2_value' => rand(90, 110),
                'q3_value' => rand(110, 130),
            ];
        }
        $data['score_distribution'] = [
            ['id' => 1, 'value' => rand(70, 110)],
            ['id' => 2, 'value' => rand(70, 110)],
            ['id' => 3, 'value' => rand(70, 110)],
            ['id' => 4, 'value' => rand(70, 110)],
        ];

        $data['list'] = [];
        $score_level = [0, 10, 18, 20]; // 模拟评分等级
        for ($i = 11; $i >= 0; $i--) {
            $data['list'][] = [
                'dep_name' => '测试数据',
                'name' => '用户',
                'fba_end_count' => rand(1000, 5000), // 模拟数据
                'fba_end_amount' => rand(1000, 5000), // 模拟数据
                'fba_end_on_way_count' => rand(1000, 5000), // 模拟数据
                'fba_end_on_way_amount' => rand(1000, 5000), // 模拟数据
                'overseas_end_count' => rand(1000, 5000), // 模拟数据
                'overseas_end_amount' => rand(1000, 5000), // 模拟数据
                'overseas_end_on_way_count' => rand(1000, 5000), // 模拟数据
                'overseas_end_on_way_amount' => rand(1000, 5000), // 模拟数据
                'total_count' => rand(1000, 5000), // 模拟数据
                'total_amount' => rand(1000, 5000), // 模拟数据
                'sales_count' => rand(1000, 5000), // 模拟数据
                'sales_amount' => rand(1000, 5000), // 模拟数据
                'turnover_days' => rand(70, 110),
                'score' => $score_level[array_rand($score_level)]
            ];
        }

        returnSuccess($data);

    }

}