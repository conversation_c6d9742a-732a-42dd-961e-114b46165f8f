<?php

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;
use plugins\logistics\utils\TimeCalculator;

/**
 * 备货建议模型
 * 处理备货建议生成、计算、存储等核心逻辑
 */
class stockSuggestionModel
{
    const IGNORE_STATUS_WAITING = 0; // 待备货
    const IGNORE_STATUS_FINISHED = 1; // 已备货
    const IGNORE_STATUS_IGNORED = 2;// 已忽略

    public $dbErp;
    public $dbLogistics;

    public function __construct()
    {
        $this->dbErp = dbErpMysql::getInstance();
        $this->dbLogistics = dbLMysql::getInstance();
    }

    /**
     * 计算单个产品的备货建议
     *
     * @param string $asin
     * @param string $country
     * @param string $weekType
     * @param string $calculationDate
     * @return array|null
     */
    public function calculateSingleProductSuggestion($data, $weekType, $calculationDate)
    {
        $asin = $data['asin'];
        $country = $data['country_code'];

        // 2. 获取销量数据
        $salesData = $this->getSalesData($asin, $country);
        if (!$salesData) {
            return null;
        }

        // 3. 获取配置数据
        $config = $this->getNormalPrepareConfig($data['product_stage'], $country);
        if (!$config) {
            return null;
        }

        // 4. 计算预估日销量
        $estimatedDailySales = $this->calculateEstimatedDailySales($salesData, $config['sales_config']);

        // 5. 计算缓冲天数
        $bufferDays = TimeCalculator::calculateBufferDays($calculationDate, $weekType, $country);
        $totalNeedDays = $config['data_range']['normal_value'] + $bufferDays;

        // 6. 获取节日配置
        $festivalConfigs = $this->getFestivalConfigs($country);

        // 7. 计算节日影响下的总需求量
        $suggestionResult = $this->calculateFestivalStockSuggestion(
            $calculationDate,
            $totalNeedDays,
            $estimatedDailySales,
            $festivalConfigs,
            $country
        );

        // 8. 计算库存阈值
        $thresholds = $this->calculateThresholds($config['data_range'], $bufferDays, $estimatedDailySales);

        // 9. 判断库存状态
        $currentStock = $data['fba_sellable_qty'] + $data['overseas_warehouse_qty'];
        $stockStatus = $this->determineStockStatus($currentStock, $thresholds);

        // 10. 计算最终建议数量
        $finalSuggestionQty = 0;
        if ($stockStatus['action'] === 'suggest') {
            $finalSuggestionQty = max(0, $suggestionResult['total_suggestion_qty'] - $currentStock);
        }

        // 11. 获取发货日期
        $shippingDate = TimeCalculator::getShippingDate($calculationDate, $weekType, $country);

        return [
            'asin' => $asin,
            'sku' => $data['sku'],
            'country_code' => $country,
            'product_stage' => $data['product_stage'],
            'suggestion_week' => $weekType,
            'current_fba_stock' => $data['fba_sellable_qty'],
            'current_overseas_stock' => $data['overseas_warehouse_qty'],
            'total_available_stock' => $currentStock,
            'sales_7_avg' => $salesData['sales_7_avg'],
            'sales_14_avg' => $salesData['sales_14_avg'],
            'sales_30_avg' => $salesData['sales_30_avg'],
            'estimated_daily_sales' => $estimatedDailySales,
            'safety_min_days' => $config['data_range']['min_value'],
            'safety_max_days' => $config['data_range']['max_value'],
            'safety_normal_days' => $config['data_range']['normal_value'],
            'buffer_days' => $bufferDays,
            'total_need_days' => $totalNeedDays,
            'min_threshold' => $thresholds['min'],
            'max_threshold' => $thresholds['max'],
            'normal_threshold' => $thresholds['normal'],
            'base_suggestion_qty' => $suggestionResult['base_suggestion_qty'],
            'festival_suggestion_qty' => $suggestionResult['festival_suggestion_qty'],
            'total_suggestion_qty' => $suggestionResult['total_suggestion_qty'],
            'final_suggestion_qty' => $finalSuggestionQty,
            'suggestion_action' => $stockStatus['action'],
            'suggestion_reason' => $stockStatus['reason'],
            'festival_configs' => json_encode($suggestionResult['festival_configs']),
            'has_festival_impact' => $suggestionResult['has_festival_impact'],
            'calculation_date' => $calculationDate,
            'shipping_date' => $shippingDate
        ];
    }


    /**
     * 获取销量数据
     */
    private function getSalesData($asin, $country)
    {
        // 这里需要根据实际的销量数据表结构来实现
        // 暂时返回模拟数据
        return [
            'sales_7_avg' => 10.5,
            'sales_14_avg' => 12.3,
            'sales_30_avg' => 11.8
        ];
    }

    /**
     * 获取normal_prepare配置
     */
    private function getNormalPrepareConfig($productStage, $country)
    {

        $configData = $this->dbLogistics->table('config')
            ->field('data')
            ->where('key_name = :key_name', [':key_name' => 'normal_prepare'])
            ->one();

        if (!$configData) {
            return null;
        }

        $configData = $configData['data'];

        $configs = json_decode($configData, true);
        foreach ($configs as $config) {
            if ($config['type'] == $productStage) {
                // 查找对应国家的配置
                foreach ($config['data_range'] as $range) {
                    if ($range['country'] === $country) {
                        return [
                            'sales_config' => $config['sales_config'],
                            'data_range' => $range
                        ];
                    }
                }
            }
        }

        return null;
    }

    /**
     * 计算预估日销量
     */
    private function calculateEstimatedDailySales($salesData, $salesConfig)
    {
        $sales7Weight = $salesConfig['sales_7'] / 100;
        $sales14Weight = $salesConfig['sales_14'] / 100;
        $sales30Weight = $salesConfig['sales_30'] / 100;

        return round($salesData['sales_7_avg'] * $sales7Weight +
            $salesData['sales_14_avg'] * $sales14Weight +
            $salesData['sales_30_avg'] * $sales30Weight);
    }

    /**
     * 获取节日配置
     */
    private function getFestivalConfigs($country)
    {
        $configData = $this->dbLogistics->table('config')
            ->field('data')
            ->where('key_name = :key_name', [':key_name' => 'festival_activity'])
            ->one();

        if (!$configData) {
            return [];
        }

        $configData = $configData['config_value'];

        $festivals = json_decode($configData, true);
        $applicableFestivals = [];

        foreach ($festivals as $festival) {
            if (in_array($country, $festival['sites_config'])) {
                $applicableFestivals[] = $festival;
            }
        }

        return $applicableFestivals;
    }

    /**
     * 计算节日影响下的备货建议
     */
    private function calculateFestivalStockSuggestion($startDate, $totalDays, $dailySales, $festivalConfigs, $country)
    {
        $baseSuggestion = $totalDays * $dailySales;
        $festivalSuggestion = 0;
        $totalSuggestion = 0;
        $hasFestivalImpact = 0;
        $affectedFestivals = [];

        for ($i = 0; $i < $totalDays; $i++) {
            $currentDate = date('Y-m-d', strtotime($startDate . " +{$i} days"));
            $dayOfYear = TimeCalculator::getMonthDayFormat($currentDate);

            $dayMultiplier = 1.0; // 基础100%
            $maxFestivalMultiplier = TimeCalculator::getMaxFestivalMultiplier($dayOfYear, $festivalConfigs, $country);

            if ($maxFestivalMultiplier > 0) {
                $hasFestivalImpact = 1;
                $festivalSuggestion += $dailySales * $maxFestivalMultiplier;

                // 记录影响的节日
                foreach ($festivalConfigs as $festival) {
                    if (in_array($country, $festival['sites_config'])) {
                        $isInFestival = TimeCalculator::isInFestivalPeriod($dayOfYear, $festival['period']);
                        $isInAdvance = TimeCalculator::isInAdvancePeriod($dayOfYear, $festival['period'], $festival['stock_rules']['day_before']);

                        if ($isInFestival || $isInAdvance) {
                            $affectedFestivals[] = $festival['name'];
                        }
                    }
                }
            }

            // 当天总倍数 = 常规100% + 最高节日倍数
            $totalMultiplier = $dayMultiplier + $maxFestivalMultiplier;
            $totalSuggestion += $dailySales * $totalMultiplier;
        }

        return [
            'base_suggestion_qty' => $baseSuggestion,
            'festival_suggestion_qty' => $festivalSuggestion,
            'total_suggestion_qty' => $totalSuggestion,
            'has_festival_impact' => $hasFestivalImpact,
            'festival_configs' => array_unique($affectedFestivals)
        ];
    }

    /**
     * 计算库存阈值
     */
    private function calculateThresholds($dataRange, $bufferDays, $estimatedDailySales)
    {
        return [
            'min' => ($dataRange['min_value'] + $bufferDays) * $estimatedDailySales,
            'max' => ($dataRange['max_value'] + $bufferDays) * $estimatedDailySales,
            'normal' => ($dataRange['normal_value'] + $bufferDays) * $estimatedDailySales
        ];
    }

    /**
     * 判断库存状态
     */
    private function determineStockStatus($currentStock, $thresholds)
    {
        if ($currentStock < $thresholds['min']) {
            return [
                'action' => 'suggest',
                'reason' => '当前库存低于安全库存最小值，需要备货'
            ];
        } elseif ($currentStock > $thresholds['max']) {
            return [
                'action' => 'redundant',
                'reason' => '当前库存超过安全库存最大值，库存冗余'
            ];
        } else {
            return [
                'action' => 'normal',
                'reason' => '当前库存在安全范围内'
            ];
        }
    }

    /**
     * 保存备货建议结果
     */
    public function saveSuggestionResult($result)
    {

        return $this->dbLogistics->table('stock_suggestion')->insert($result);
    }

    /**
     * 清理过期的备货建议数据
     */
    public function cleanExpiredSuggestions($daysToKeep = 7)
    {
        $expireDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));

        return $this->dbLogistics->table('stock_suggestion')
            ->where('calculation_date < :expire_date', [':expire_date' => $expireDate])
            ->delete();
    }

    /**
     * 更新忽略状态
     *
     * @param string $id
     * @param int $ignoreStatus 1=忽略, 0=取消忽略
     * @return bool
     */
    public function updateIgnoreStatus($id, $ignoreStatus)
    {
        $updateData = [
            'ignore_status' => $ignoreStatus,
        ];


        $this->dbLogistics->table('stock_suggestion')
            ->where('id = :id', [
                ':id' => $id,
            ])
            ->update($updateData);

        return true;
    }

    /**
     * 检查产品是否被忽略
     */
    public function isProductIgnored($id, $calculationDate = null)
    {
        if (!$calculationDate) {
            $calculationDate = date('Y-m-d');
        }

        $result = $this->dbLogistics->table('stock_suggestion')
            ->field('ignore_status')
            ->where('id=:id AND calculation_date = :calculation_date', [
                ':id' => $id,
                ':calculation_date' => $calculationDate
            ])
            ->one();

        return isset($result['ignore_status']) && $result['ignore_status'] == 1;
    }

    /**
     * 获取备货建议列表（支持忽略状态筛选）
     */
    public function getStockSuggestionList($filters = [], $page = 1, $pageSize = 100)
    {
        $whereConditions = ["1=1"];
        $params = [];

        // 构建筛选条件
        if (!empty($filters['country_code'])) {
            $whereConditions[] = "country_code = :country_code";
            $params[':country_code'] = $filters['country_code'];
        }

        if (!empty($filters['suggestion_week'])) {
            $whereConditions[] = "suggestion_week = :suggestion_week";
            $params[':suggestion_week'] = $filters['suggestion_week'];
        }

        if (!empty($filters['suggestion_action'])) {
            $whereConditions[] = "suggestion_action = :suggestion_action";
            $params[':suggestion_action'] = $filters['suggestion_action'];
        }

        if (isset($filters['ignore_status']) && $filters['ignore_status'] !== '') {
            $whereConditions[] = "ignore_status = :ignore_status";
            $params[':ignore_status'] = $filters['ignore_status'];
        }

        if (!empty($filters['asin'])) {
            $whereConditions[] = "asin LIKE :asin";
            $params[':asin'] = '%' . $filters['asin'] . '%';
        }

        if (!empty($filters['calculation_date'])) {
            $whereConditions[] = "calculation_date = :calculation_date";
            $params[':calculation_date'] = $filters['calculation_date'];
        } else {
            // 默认查询当天数据
            $whereConditions[] = "calculation_date = :calculation_date";
            $params[':calculation_date'] = date('Y-m-d');
        }

        // 构建查询条件字符串
        $whereClause = implode(' AND ', $whereConditions);

        // 使用dbLMysql的分页功能
        $result = $this->dbLogistics->table('stock_suggestion')
            ->where($whereClause, $params)
            ->order('final_suggestion_qty DESC, asin')
            ->pages($page, $pageSize);

        return [
            'total_count' => $result['total'],
            'page' => $result['page'],
            'page_size' => $pageSize,
            'total_pages' => ceil($result['total'] / $pageSize),
            'list' => $result['list']
        ];


    }

}
