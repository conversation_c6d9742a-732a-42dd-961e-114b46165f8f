<?php

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;

/**
 * 采购建议模型
 * 处理采购建议生成、计算、存储等核心逻辑
 */
class purchaseSuggestionModel
{
    private $dbLogistics;
    private $dbErp;

    public function __construct()
    {
        $this->dbLogistics = dbLMysql::getInstance();
        $this->dbErp = dbErpMysql::getInstance();
    }

    /**
     * 生成采购建议
     * @param string $calculationDate 计算日期
     * @return array
     */
    public function generatePurchaseSuggestion($calculationDate)
    {
        try {
            $this->dbLogistics->beginTransaction();
            
            // 1. 获取需要计算的产品数据
            $products = $this->getProductsForPurchaseCalculation($calculationDate);
            
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            
            foreach ($products as $product) {
                try {
                    // 2. 计算单个产品的采购建议
                    $suggestion = $this->calculateSingleProductPurchase($product, $calculationDate);
                    
                    if ($suggestion) {
                        // 3. 保存采购建议
                        $this->savePurchaseSuggestion($suggestion);
                        $successCount++;
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "SKU {$product['sku']} 处理失败: " . $e->getMessage();
                }
            }
            
            $this->dbLogistics->commit();
            
            return [
                'success' => true,
                'total_processed' => count($products),
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];
            
        } catch (\Exception $e) {
            $this->dbLogistics->rollBack();
            return [
                'success' => false,
                'message' => '采购建议生成失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 计算单个产品的采购建议
     * @param array $product 产品数据
     * @param string $calculationDate 计算日期
     * @return array|null
     */
    public function calculateSingleProductPurchase($product, $calculationDate)
    {
        $sku = $product['sku'];
        $site = $product['site'];
        
        // 1. 获取备货建议数据
        $stockSuggestion = $this->getStockSuggestionData($sku, $site, $calculationDate);
        
        if (!$stockSuggestion || $stockSuggestion['suggestion_action'] !== 'suggest') {
            return null; // 不需要采购
        }
        
        // 2. 获取当前库存状态
        $currentStock = $this->getCurrentStockStatus($sku, $site);
        
        // 3. 获取在途库存
        $inTransitStock = $this->getInTransitStock($sku, $site);
        
        // 4. 计算采购需求量
        $purchaseQty = $this->calculatePurchaseQuantity($stockSuggestion, $currentStock, $inTransitStock);
        
        if ($purchaseQty <= 0) {
            return null; // 不需要采购
        }
        
        // 5. 获取产品优先级
        $priority = $this->getProductPriority($product);
        
        // 6. 计算冗余量
        $redundantQty = $this->calculateRedundantQuantity($currentStock, $stockSuggestion);
        
        return [
            'sku' => $sku,
            'site' => $site,
            'product_name' => $product['product_name'] ?? '',
            'product_stage' => $product['product_stage'] ?? 'stable',
            'calculation_date' => $calculationDate,
            'current_fba_stock' => $currentStock['fba_stock'],
            'current_overseas_stock' => $currentStock['overseas_stock'],
            'total_current_stock' => $currentStock['total_stock'],
            'in_transit_stock' => $inTransitStock,
            'suggested_stock' => $stockSuggestion['suggested_stock'],
            'purchase_qty' => $purchaseQty,
            'redundant_qty' => $redundantQty,
            'priority' => $priority,
            'status' => 'pending',
            'estimated_daily_sales' => $stockSuggestion['estimated_daily_sales'],
            'safety_stock_days' => $stockSuggestion['safety_stock_days'] ?? 85,
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 获取需要计算采购建议的产品数据
     * @param string $calculationDate 计算日期
     * @return array
     */
    private function getProductsForPurchaseCalculation($calculationDate)
    {
        // 从FBA库存汇总表获取产品数据
        $sql = "SELECT 
                    fss.sku,
                    fss.asin,
                    fss.country_code as site,
                    fss.product_stage,
                    fss.fba_sellable_qty,
                    fss.overseas_warehouse_qty,
                    g.product_name
                FROM fba_storage_summary fss
                LEFT JOIN financial.goods g ON fss.sku = g.sku
                WHERE fss.is_active = 1
                AND fss.sku IS NOT NULL
                AND fss.sku != ''";
        
        return $this->dbLogistics->query($sql, [], true) ?: [];
    }

    /**
     * 获取备货建议数据
     * @param string $sku SKU
     * @param string $site 站点
     * @param string $calculationDate 计算日期
     * @return array|null
     */
    private function getStockSuggestionData($sku, $site, $calculationDate)
    {
        return $this->dbLogistics->table('stock_suggestion')
            ->where('sku = :sku AND country_code = :site AND calculation_date = :date', [
                'sku' => $sku,
                'site' => $site,
                'date' => $calculationDate
            ])
            ->one();
    }

    /**
     * 获取当前库存状态
     * @param string $sku SKU
     * @param string $site 站点
     * @return array
     */
    private function getCurrentStockStatus($sku, $site)
    {
        $fbaStock = $this->dbLogistics->table('fba_storage_summary')
            ->where('sku = :sku AND country_code = :site', ['sku' => $sku, 'site' => $site])
            ->one();
        
        $fbaQty = $fbaStock['fba_sellable_qty'] ?? 0;
        $overseasQty = $fbaStock['overseas_warehouse_qty'] ?? 0;
        
        return [
            'fba_stock' => $fbaQty,
            'overseas_stock' => $overseasQty,
            'total_stock' => $fbaQty + $overseasQty
        ];
    }

    /**
     * 获取在途库存
     * @param string $sku SKU
     * @param string $site 站点
     * @return int
     */
    private function getInTransitStock($sku, $site)
    {
        // 从发货建议或入库单获取在途库存
        $sql = "SELECT COALESCE(SUM(quantity), 0) as in_transit_qty
                FROM shipping_suggestion 
                WHERE sku = :sku 
                AND site = :site 
                AND status IN ('processing', 'shipped')";
        
        $result = $this->dbLogistics->query($sql, [
            'sku' => $sku,
            'site' => $site
        ]);
        
        return $result['in_transit_qty'] ?? 0;
    }

    /**
     * 计算采购需求量
     * @param array $stockSuggestion 备货建议
     * @param array $currentStock 当前库存
     * @param int $inTransitStock 在途库存
     * @return int
     */
    private function calculatePurchaseQuantity($stockSuggestion, $currentStock, $inTransitStock)
    {
        $suggestedStock = $stockSuggestion['suggested_stock'] ?? 0;
        $totalAvailableStock = $currentStock['total_stock'] + $inTransitStock;
        
        return max(0, $suggestedStock - $totalAvailableStock);
    }

    /**
     * 计算冗余量
     * @param array $currentStock 当前库存
     * @param array $stockSuggestion 备货建议
     * @return int
     */
    private function calculateRedundantQuantity($currentStock, $stockSuggestion)
    {
        $suggestedStock = $stockSuggestion['suggested_stock'] ?? 0;
        $totalStock = $currentStock['total_stock'];
        
        return max(0, $totalStock - $suggestedStock);
    }

    /**
     * 获取产品优先级
     * @param array $product 产品数据
     * @return string
     */
    private function getProductPriority($product)
    {
        $productStage = $product['product_stage'] ?? 'stable';
        
        // 优先级配置：stable > decline > growth > new
        $priorityMap = [
            'stable' => 'high',
            'decline' => 'medium',
            'growth' => 'medium',
            'new' => 'low'
        ];
        
        return $priorityMap[$productStage] ?? 'medium';
    }

    /**
     * 保存采购建议
     * @param array $suggestion 建议数据
     * @return bool
     */
    private function savePurchaseSuggestion($suggestion)
    {
        // 使用UPSERT逻辑
        $this->dbLogistics->table('purchase_suggestion')
            ->where('sku = :sku AND site = :site AND calculation_date = :date', [
                'sku' => $suggestion['sku'],
                'site' => $suggestion['site'],
                'date' => $suggestion['calculation_date']
            ])
            ->delete();
        
        return $this->dbLogistics->table('purchase_suggestion')->insert($suggestion);
    }

    /**
     * 获取采购建议列表
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getPurchaseSuggestionList($filters = [], $page = 1, $pageSize = 20)
    {
        $whereConditions = ['1=1'];
        $params = [];

        // SKU筛选
        if (!empty($filters['sku'])) {
            $whereConditions[] = "sku LIKE :sku";
            $params['sku'] = '%' . $filters['sku'] . '%';
        }

        // 站点筛选
        if (!empty($filters['site'])) {
            $whereConditions[] = "site = :site";
            $params['site'] = $filters['site'];
        }

        // 计算日期筛选
        if (!empty($filters['calculation_date'])) {
            $whereConditions[] = "calculation_date = :calculation_date";
            $params['calculation_date'] = $filters['calculation_date'];
        }

        // 优先级筛选
        if (!empty($filters['priority'])) {
            $whereConditions[] = "priority = :priority";
            $params['priority'] = $filters['priority'];
        }

        // 状态筛选
        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $filters['status'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $result = $this->dbLogistics->table('purchase_suggestion')
            ->where($whereClause, $params)
            ->order('priority DESC, purchase_qty DESC, calculation_date DESC')
            ->pages($page, $pageSize);

        return [
            'total_count' => $result['total'],
            'page' => $result['page'],
            'page_size' => $pageSize,
            'total_pages' => ceil($result['total'] / $pageSize),
            'list' => $result['list']
        ];
    }

    /**
     * 更新采购建议状态
     * @param int $id 建议ID
     * @param string $status 新状态
     * @return bool
     */
    public function updateSuggestionStatus($id, $status)
    {
        $allowedStatus = ['pending', 'purchased', 'ignored'];
        if (!in_array($status, $allowedStatus)) {
            return false;
        }

        return $this->dbLogistics->table('purchase_suggestion')
            ->where('id = :id', ['id' => $id])
            ->update([
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 批量更新采购建议状态
     * @param array $ids 建议ID数组
     * @param string $status 新状态
     * @return bool
     */
    public function batchUpdateStatus($ids, $status)
    {
        if (empty($ids)) {
            return false;
        }

        $allowedStatus = ['pending', 'purchased', 'ignored'];
        if (!in_array($status, $allowedStatus)) {
            return false;
        }

        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE purchase_suggestion
                SET status = ?, updated_at = NOW()
                WHERE id IN ({$placeholders})";

        $params = array_merge([$status], $ids);
        return $this->dbLogistics->execute($sql, $params);
    }

    /**
     * 获取采购建议详情
     * @param int $id 建议ID
     * @return array|false
     */
    public function getSuggestionDetail($id)
    {
        return $this->dbLogistics->table('purchase_suggestion')
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    /**
     * 获取采购建议统计
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getPurchaseStatistics($startDate, $endDate)
    {
        $sql = "SELECT
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN status = 'purchased' THEN 1 ELSE 0 END) as purchased_count,
                    SUM(CASE WHEN status = 'ignored' THEN 1 ELSE 0 END) as ignored_count,
                    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority_count,
                    SUM(purchase_qty) as total_purchase_qty,
                    AVG(purchase_qty) as avg_purchase_qty,
                    COUNT(DISTINCT sku) as unique_sku_count,
                    COUNT(DISTINCT site) as unique_site_count
                FROM purchase_suggestion
                WHERE calculation_date BETWEEN :start_date AND :end_date";

        return $this->dbLogistics->query($sql, [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]) ?: [];
    }

    /**
     * 获取SKU的采购历史趋势
     * @param string $sku SKU
     * @param string $site 站点
     * @param int $days 查询天数
     * @return array
     */
    public function getSkuPurchaseTrend($sku, $site, $days = 30)
    {
        $sql = "SELECT
                    calculation_date,
                    purchase_qty,
                    redundant_qty,
                    status,
                    priority,
                    estimated_daily_sales,
                    total_current_stock
                FROM purchase_suggestion
                WHERE sku = :sku
                AND site = :site
                AND calculation_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                ORDER BY calculation_date DESC";

        return $this->dbLogistics->query($sql, [
            'sku' => $sku,
            'site' => $site,
            'days' => $days
        ], true) ?: [];
    }

    /**
     * 删除过期的采购建议
     * @param int $days 保留天数
     * @return int 删除数量
     */
    public function cleanExpiredSuggestions($days = 30)
    {
        $sql = "DELETE FROM purchase_suggestion
                WHERE calculation_date < DATE_SUB(CURDATE(), INTERVAL :days DAY)
                AND status IN ('purchased', 'ignored')";

        $this->dbLogistics->execute($sql, ['days' => $days]);
        return $this->dbLogistics->rowCount();
    }

    /**
     * 重新计算指定产品的采购建议
     * @param string $sku SKU
     * @param string $site 站点
     * @param string $calculationDate 计算日期
     * @return array
     */
    public function recalculateSingleProduct($sku, $site, $calculationDate)
    {
        try {
            // 获取产品数据
            $product = $this->getProductDataForPurchase($sku, $site);
            if (!$product) {
                return ['success' => false, 'message' => '产品数据不存在'];
            }

            // 重新计算建议
            $suggestion = $this->calculateSingleProductPurchase($product, $calculationDate);

            if ($suggestion) {
                // 保存建议
                $this->savePurchaseSuggestion($suggestion);
                return ['success' => true, 'data' => $suggestion];
            } else {
                return ['success' => false, 'message' => '不需要采购或计算失败'];
            }
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取单个产品数据（用于采购计算）
     * @param string $sku SKU
     * @param string $site 站点
     * @return array|null
     */
    private function getProductDataForPurchase($sku, $site)
    {
        $sql = "SELECT
                    fss.sku,
                    fss.asin,
                    fss.country_code as site,
                    fss.product_stage,
                    fss.fba_sellable_qty,
                    fss.overseas_warehouse_qty,
                    g.product_name
                FROM fba_storage_summary fss
                LEFT JOIN financial.goods g ON fss.sku = g.sku
                WHERE fss.sku = :sku
                AND fss.country_code = :site
                AND fss.is_active = 1";

        return $this->dbLogistics->query($sql, [
            'sku' => $sku,
            'site' => $site
        ]);
    }

    /**
     * 获取特殊产品配置（覆盖优先级）
     * @param string $sku SKU
     * @param string $site 站点
     * @return array|null
     */
    public function getSpecialProductConfig($sku, $site)
    {
        return $this->dbLogistics->table('special_product')
            ->where('sku = :sku AND site = :site', ['sku' => $sku, 'site' => $site])
            ->one();
    }

    /**
     * 应用特殊产品配置
     * @param array $suggestion 原始建议
     * @param array $specialConfig 特殊配置
     * @return array
     */
    public function applySpecialProductConfig($suggestion, $specialConfig)
    {
        if (!$specialConfig) {
            return $suggestion;
        }

        // 覆盖优先级
        if (!empty($specialConfig['priority_override'])) {
            $suggestion['priority'] = $specialConfig['priority_override'];
        }

        // 覆盖采购量
        if (!empty($specialConfig['purchase_qty_override'])) {
            $suggestion['purchase_qty'] = $specialConfig['purchase_qty_override'];
        }

        // 添加特殊标记
        $suggestion['has_special_config'] = true;
        $suggestion['special_config'] = $specialConfig;

        return $suggestion;
    }

    /**
     * 计算单个SKU的采购建议（支持多站点）
     * @param array $skuCountry SKU国家数据
     * @param array $skuCountryList SKU下的所有国家列表
     * @param string $weekType 周类型
     * @param string $calculationDate 计算日期
     * @return array|null
     */
    public function calculateSingleSkuPurchaseSuggestion($skuCountry, $skuCountryList, $weekType, $calculationDate)
    {
        $sku = $skuCountry['sku'];

        // 汇总所有站点的采购需求
        $totalPurchaseQty = 0;
        $siteDetails = [];

        foreach ($skuCountryList as $country) {
            $siteData = $this->calculateSingleProductPurchase($country, $calculationDate);
            if ($siteData && $siteData['purchase_qty'] > 0) {
                $totalPurchaseQty += $siteData['purchase_qty'];
                $siteDetails[] = $siteData;
            }
        }

        if ($totalPurchaseQty <= 0) {
            return null;
        }

        // 获取最高优先级
        $highestPriority = $this->getHighestPriority($siteDetails);

        return [
            'sku' => $sku,
            'total_purchase_qty' => $totalPurchaseQty,
            'site_count' => count($siteDetails),
            'site_details' => $siteDetails,
            'priority' => $highestPriority,
            'suggestion_action' => 'suggest',
            'calculation_date' => $calculationDate,
            'week_type' => $weekType,
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 获取最高优先级
     * @param array $siteDetails 站点详情
     * @return string
     */
    private function getHighestPriority($siteDetails)
    {
        $priorityOrder = ['high' => 3, 'medium' => 2, 'low' => 1];
        $maxPriority = 0;
        $result = 'low';

        foreach ($siteDetails as $site) {
            $priority = $site['priority'] ?? 'low';
            if ($priorityOrder[$priority] > $maxPriority) {
                $maxPriority = $priorityOrder[$priority];
                $result = $priority;
            }
        }

        return $result;
    }

    /**
     * 保存采购建议结果
     * @param array $result 建议结果
     * @return bool
     */
    public function savePurchaseSuggestionResult($result)
    {
        // 保存到采购建议结果表
        return $this->dbLogistics->table('purchase_suggestion_result')->insert([
            'sku' => $result['sku'],
            'total_purchase_qty' => $result['total_purchase_qty'],
            'site_count' => $result['site_count'],
            'priority' => $result['priority'],
            'suggestion_action' => $result['suggestion_action'],
            'calculation_date' => $result['calculation_date'],
            'week_type' => $result['week_type'],
            'site_details' => json_encode($result['site_details'], JSON_UNESCAPED_UNICODE),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 清理周数据
     * @return int 删除数量
     */
    public function cleanWeeklyData()
    {
        // 删除7天前的数据
        $sql = "DELETE FROM purchase_suggestion
                WHERE calculation_date < DATE_SUB(CURDATE(), INTERVAL 7 DAY)";

        $this->dbLogistics->execute($sql);
        return $this->dbLogistics->rowCount();
    }
}
