<?php

require_once dirname(__DIR__, 3) . '/core/lib/db/dbLMysql.php';
require_once dirname(__DIR__, 3) . '/core/lib/db/dbErpMysql.php';
require_once dirname(__DIR__) . '/utils/TimeCalculator.php';

/**
 * 发货建议模型
 * 处理发货建议生成、店铺分配、约束检查等核心逻辑
 */
class ShippingSuggestionModel
{
    private $dbLogistics;
    private $dbErp;

    public function __construct()
    {
        $this->dbLogistics = dbLMysql::getInstance();
        $this->dbErp = dbErpMysql::getInstance();
    }

    /**
     * 生成发货建议
     * @param string $calculationDate 计算日期
     * @param string $weekType 周类型 current/next
     * @return array
     */
    public function generateShippingSuggestion($calculationDate, $weekType = 'current')
    {
        try {
            $this->dbLogistics->beginTransaction();
            
            // 1. 获取需要发货的产品数据
            $products = $this->getProductsNeedShipping($calculationDate, $weekType);
            
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            
            foreach ($products as $product) {
                try {
                    // 2. 计算单个产品的发货建议
                    $suggestion = $this->calculateSingleProductShipping($product, $calculationDate, $weekType);
                    
                    if ($suggestion) {
                        // 3. 保存发货建议
                        $this->saveSuggestion($suggestion);
                        $successCount++;
                    }
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "SKU {$product['sku']} 处理失败: " . $e->getMessage();
                }
            }
            
            $this->dbLogistics->commit();
            
            return [
                'success' => true,
                'total_processed' => count($products),
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            $this->dbLogistics->rollBack();
            return [
                'success' => false,
                'message' => '发货建议生成失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 计算单个产品的发货建议
     * @param array $product 产品数据
     * @param string $calculationDate 计算日期
     * @param string $weekType 周类型
     * @return array|null
     */
    public function calculateSingleProductShipping($product, $calculationDate, $weekType)
    {
        $sku = $product['sku'];
        $site = $product['country_code'];
        
        // 1. 计算补货量
        $suggestedStock = $this->calculateSuggestedStock($product);
        if ($suggestedStock <= 0) {
            return null; // 不需要补货
        }
        
        // 2. 判断是否紧急补货
        $urgentFlag = $this->isUrgentShipping($product);
        
        // 3. 确定发货方式和路径
        $shippingMethod = $urgentFlag ? 'air' : 'sea';
        $shippingRoute = $this->determineShippingRoute($sku, $site, $urgentFlag);
        
        // 4. 计算店铺分配
        $allocation = $this->calculateShopAllocation($sku, $site, $suggestedStock, $product);
        
        return [
            'sku' => $sku,
            'site' => $site,
            'asin' => $product['asin'] ?? '',
            'product_name' => $product['product_name'] ?? '',
            'current_stock' => $product['fba_sellable_qty'] + $product['overseas_warehouse_qty'],
            'suggested_stock' => $suggestedStock,
            'shipping_method' => $shippingMethod,
            'shipping_route' => $shippingRoute,
            'urgent_flag' => $urgentFlag ? 1 : 0,
            'attach' => json_encode($allocation),
            'status' => 'pending',
            'suggestion_date' => $calculationDate
        ];
    }

    /**
     * 计算店铺分配
     * @param string $sku SKU
     * @param string $site 站点
     * @param int $totalStock 总补货量
     * @param array $product 产品数据
     * @return array
     */
    public function calculateShopAllocation($sku, $site, $totalStock, $product)
    {
        // 1. 获取产品阶段配置
        $stageConfig = $this->getProductStageAllocation($product['product_stage'] ?? 'stable');
        
        // 2. 获取店铺列表
        $shops = $this->getActiveShops($site);
        
        // 3. 按类型分配
        $typeAllocations = $this->allocateByType($totalStock, $stageConfig);
        
        // 4. 类型内按店铺分配
        $shopAllocations = [];
        $allocationSummary = [];
        $unallocatedQuantity = 0;
        
        foreach ($typeAllocations as $type => $typeQuantity) {
            $typeShops = array_filter($shops, function($shop) use ($type) {
                return $shop['shop_type'] === $type;
            });
            
            if (empty($typeShops)) {
                $unallocatedQuantity += $typeQuantity;
                continue;
            }
            
            // 按销量比例分配
            $typeAllocation = $this->allocateWithinType($typeShops, $typeQuantity, $sku, $site);
            $shopAllocations = array_merge($shopAllocations, $typeAllocation['allocations']);
            $allocationSummary[$type . '_total'] = $typeAllocation['allocated_total'];
            $unallocatedQuantity += $typeAllocation['unallocated'];
        }
        
        // 5. 处理未分配库存
        if ($unallocatedQuantity > 0) {
            $backupAllocation = $this->allocateToBackupShops($shops, $unallocatedQuantity, $sku, $site);
            $shopAllocations = array_merge($shopAllocations, $backupAllocation['allocations']);
            $unallocatedQuantity = $backupAllocation['unallocated'];
        }
        
        // 6. 约束检查
        $constraintsCheck = $this->checkAllocationConstraints($shopAllocations, $sku, $site);
        
        return [
            'total_allocation' => $totalStock,
            'shop_allocations' => $shopAllocations,
            'allocation_summary' => $allocationSummary,
            'constraints_check' => [
                'inventory_limit_passed' => $constraintsCheck['inventory_passed'],
                'revenue_limit_passed' => $constraintsCheck['revenue_passed'],
                'unallocated_quantity' => $unallocatedQuantity
            ]
        ];
    }

    /**
     * 按类型分配库存
     * @param int $totalStock 总库存
     * @param array $stageConfig 阶段配置
     * @return array
     */
    private function allocateByType($totalStock, $stageConfig)
    {
        return [
            'main_backup' => intval($totalStock * $stageConfig['main_backup_ratio'] / 100),
            'sub_backup' => intval($totalStock * $stageConfig['sub_backup_ratio'] / 100),
            'review' => intval($totalStock * $stageConfig['review_ratio'] / 100),
            'brush' => intval($totalStock * $stageConfig['brush_ratio'] / 100)
        ];
    }

    /**
     * 类型内分配
     * @param array $shops 店铺列表
     * @param int $typeQuantity 类型总量
     * @param string $sku SKU
     * @param string $site 站点
     * @return array
     */
    private function allocateWithinType($shops, $typeQuantity, $sku, $site)
    {
        $allocations = [];
        $totalSales = array_sum(array_column($shops, 'daily_sales_avg'));
        $allocatedTotal = 0;
        
        if ($totalSales <= 0) {
            // 平均分配
            $avgQuantity = intval($typeQuantity / count($shops));
            foreach ($shops as $shop) {
                $quantity = min($avgQuantity, $typeQuantity - $allocatedTotal);
                if ($quantity > 0) {
                    $allocations[] = [
                        'shop_code' => $shop['shop_code'],
                        'shop_type' => $shop['shop_type'],
                        'allocated_quantity' => $quantity,
                        'current_stock' => $this->getShopCurrentStock($shop['shop_code'], $sku),
                        'risk_status' => $shop['risk_status'],
                        'allocation_reason' => '平均分配'
                    ];
                    $allocatedTotal += $quantity;
                }
            }
        } else {
            // 按销量比例分配
            foreach ($shops as $shop) {
                $ratio = $shop['daily_sales_avg'] / $totalSales;
                $quantity = intval($typeQuantity * $ratio);
                
                if ($quantity > 0) {
                    $allocations[] = [
                        'shop_code' => $shop['shop_code'],
                        'shop_type' => $shop['shop_type'],
                        'allocated_quantity' => $quantity,
                        'current_stock' => $this->getShopCurrentStock($shop['shop_code'], $sku),
                        'risk_status' => $shop['risk_status'],
                        'allocation_reason' => '按销量比例分配'
                    ];
                    $allocatedTotal += $quantity;
                }
            }
        }
        
        return [
            'allocations' => $allocations,
            'allocated_total' => $allocatedTotal,
            'unallocated' => $typeQuantity - $allocatedTotal
        ];
    }

    /**
     * 分配到备货店铺
     * @param array $shops 所有店铺
     * @param int $quantity 待分配数量
     * @param string $sku SKU
     * @param string $site 站点
     * @return array
     */
    private function allocateToBackupShops($shops, $quantity, $sku, $site)
    {
        $backupShops = array_filter($shops, function($shop) {
            return in_array($shop['shop_type'], ['main_backup', 'sub_backup']) && $shop['risk_status'] === 'normal';
        });
        
        if (empty($backupShops)) {
            return ['allocations' => [], 'unallocated' => $quantity];
        }
        
        return $this->allocateWithinType($backupShops, $quantity, $sku, $site);
    }

    /**
     * 检查分配约束
     * @param array $allocations 分配结果
     * @param string $sku SKU
     * @param string $site 站点
     * @return array
     */
    private function checkAllocationConstraints($allocations, $sku, $site)
    {
        $inventoryPassed = true;
        $revenuePassed = true;
        
        foreach ($allocations as $allocation) {
            $shop = $this->getShopConfig($allocation['shop_code']);
            
            // 检查库存限制（风险店铺）
            if ($shop['risk_status'] === 'risk') {
                $totalStock = $this->getShopTotalStock($allocation['shop_code']) + $allocation['allocated_quantity'];
                if ($totalStock > $shop['inventory_limit']) {
                    $inventoryPassed = false;
                }
            }
            
            // 检查流水限制
            $productPrice = $this->getProductPrice($sku, $site);
            $revenue = $allocation['allocated_quantity'] * $productPrice;
            if ($revenue > $shop['revenue_limit']) {
                $revenuePassed = false;
            }
        }
        
        return [
            'inventory_passed' => $inventoryPassed,
            'revenue_passed' => $revenuePassed
        ];
    }

    /**
     * 获取需要发货的产品数据
     * @param string $calculationDate 计算日期
     * @param string $weekType 周类型
     * @return array
     */
    private function getProductsNeedShipping($calculationDate, $weekType)
    {
        // 从备货建议表获取需要发货的产品
        $sql = "SELECT
                    ss.sku,
                    ss.asin,
                    ss.country_code,
                    ss.product_stage,
                    ss.current_fba_stock as fba_sellable_qty,
                    ss.current_overseas_stock as overseas_warehouse_qty,
                    ss.final_suggestion_qty,
                    ss.estimated_daily_sales,
                    g.product_name
                FROM stock_suggestion ss
                LEFT JOIN financial.goods g ON ss.sku = g.sku
                WHERE ss.calculation_date = :calculation_date
                AND ss.suggestion_week = :week_type
                AND ss.final_suggestion_qty > 0
                AND ss.suggestion_action = 'suggest'";

        return $this->dbLogistics->query($sql, [
            'calculation_date' => $calculationDate,
            'week_type' => $weekType
        ], true) ?: [];
    }

    /**
     * 计算建议补货量
     * @param array $product 产品数据
     * @return int
     */
    private function calculateSuggestedStock($product)
    {
        return max(0, intval($product['final_suggestion_qty']));
    }

    /**
     * 判断是否紧急补货
     * @param array $product 产品数据
     * @return bool
     */
    private function isUrgentShipping($product)
    {
        $currentStock = $product['fba_sellable_qty'] + $product['overseas_warehouse_qty'];
        $dailySales = $product['estimated_daily_sales'] ?? 0;

        if ($dailySales <= 0) {
            return false;
        }

        $availableDays = $currentStock / $dailySales;
        return $availableDays < 20; // 小于20天为紧急
    }

    /**
     * 确定发货路径
     * @param string $sku SKU
     * @param string $site 站点
     * @param bool $isUrgent 是否紧急
     * @return string
     */
    private function determineShippingRoute($sku, $site, $isUrgent)
    {
        if ($isUrgent) {
            return 'direct'; // 紧急补货直发
        }

        // 查询历史发货路径
        $sql = "SELECT
                    shipping_route,
                    COUNT(*) as count
                FROM shipping_route_history
                WHERE sku = :sku AND site = :site
                AND is_urgent = 0
                AND shipping_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                GROUP BY shipping_route
                ORDER BY count DESC";

        $history = $this->dbLogistics->query($sql, [
            'sku' => $sku,
            'site' => $site
        ], true);

        if (empty($history)) {
            return 'direct'; // 无历史记录默认直发
        }

        $total = array_sum(array_column($history, 'count'));
        $topRoute = $history[0];

        // 如果最高占比路径超过80%，使用该路径
        if ($topRoute['count'] / $total >= 0.8) {
            return $topRoute['shipping_route'];
        }

        return 'direct'; // 默认直发
    }

    /**
     * 获取产品阶段分配配置
     * @param string $productStage 产品阶段
     * @return array
     */
    private function getProductStageAllocation($productStage)
    {
        $config = $this->dbLogistics->table('product_stage_allocation')
            ->where('product_stage = :stage AND is_active = 1', ['stage' => $productStage])
            ->one();

        if (!$config) {
            // 默认配置
            return [
                'main_backup_ratio' => 25.0,
                'sub_backup_ratio' => 15.0,
                'review_ratio' => 50.0,
                'brush_ratio' => 10.0
            ];
        }

        return $config;
    }

    /**
     * 获取活跃店铺列表
     * @param string $site 站点
     * @return array
     */
    private function getActiveShops($site)
    {
        return $this->dbLogistics->table('shop_config')
            ->where('site = :site AND is_active = 1', ['site' => $site])
            ->list() ?: [];
    }

    /**
     * 获取店铺当前库存
     * @param string $shopCode 店铺代码
     * @param string $sku SKU
     * @return int
     */
    private function getShopCurrentStock($shopCode, $sku)
    {
        // 这里需要根据实际的店铺库存表结构来实现
        // 暂时返回0，后续可以对接实际的库存数据
        return 0;
    }

    /**
     * 获取店铺配置
     * @param string $shopCode 店铺代码
     * @return array
     */
    private function getShopConfig($shopCode)
    {
        return $this->dbLogistics->table('shop_config')
            ->where('shop_code = :code', ['code' => $shopCode])
            ->one() ?: [];
    }

    /**
     * 获取店铺总库存
     * @param string $shopCode 店铺代码
     * @return int
     */
    private function getShopTotalStock($shopCode)
    {
        // 这里需要根据实际的店铺库存表结构来实现
        // 暂时返回0，后续可以对接实际的库存数据
        return 0;
    }

    /**
     * 获取产品价格
     * @param string $sku SKU
     * @param string $site 站点
     * @return float
     */
    private function getProductPrice($sku, $site)
    {
        // 从商品表获取价格
        $sql = "SELECT price FROM financial.goods WHERE sku = :sku LIMIT 1";
        $result = $this->dbLogistics->query($sql, ['sku' => $sku]);
        return $result ? floatval($result['price']) : 0.0;
    }

    /**
     * 保存发货建议
     * @param array $suggestion 建议数据
     * @return bool
     */
    private function saveSuggestion($suggestion)
    {
        // 使用UPSERT逻辑
        $sql = "INSERT INTO shipping_suggestion
                (sku, site, asin, product_name, current_stock, suggested_stock,
                 shipping_method, shipping_route, urgent_flag, attach, status, suggestion_date)
                VALUES
                (:sku, :site, :asin, :product_name, :current_stock, :suggested_stock,
                 :shipping_method, :shipping_route, :urgent_flag, :attach, :status, :suggestion_date)
                ON DUPLICATE KEY UPDATE
                current_stock = VALUES(current_stock),
                suggested_stock = VALUES(suggested_stock),
                shipping_method = VALUES(shipping_method),
                shipping_route = VALUES(shipping_route),
                urgent_flag = VALUES(urgent_flag),
                attach = VALUES(attach),
                updated_at = CURRENT_TIMESTAMP";

        return $this->dbLogistics->execute($sql, $suggestion);
    }

    /**
     * 获取发货建议列表
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getShippingSuggestionList($filters = [], $page = 1, $pageSize = 20)
    {
        $whereConditions = ['1=1'];
        $params = [];

        // SKU筛选
        if (!empty($filters['sku'])) {
            $whereConditions[] = "sku LIKE :sku";
            $params['sku'] = '%' . $filters['sku'] . '%';
        }

        // 站点筛选
        if (!empty($filters['site'])) {
            $whereConditions[] = "site = :site";
            $params['site'] = $filters['site'];
        }

        // 状态筛选
        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $filters['status'];
        }

        // 建议日期筛选
        if (!empty($filters['suggestion_date'])) {
            $whereConditions[] = "suggestion_date = :suggestion_date";
            $params['suggestion_date'] = $filters['suggestion_date'];
        }

        // 发货方式筛选
        if (!empty($filters['shipping_method'])) {
            $whereConditions[] = "shipping_method = :shipping_method";
            $params['shipping_method'] = $filters['shipping_method'];
        }

        // 紧急标志筛选
        if (isset($filters['urgent_flag'])) {
            $whereConditions[] = "urgent_flag = :urgent_flag";
            $params['urgent_flag'] = $filters['urgent_flag'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $result = $this->dbLogistics->table('shipping_suggestion')
            ->where($whereClause, $params)
            ->order('suggestion_date DESC, urgent_flag DESC, suggested_stock DESC')
            ->pages($page, $pageSize);

        // 解析attach字段
        if (!empty($result['list'])) {
            foreach ($result['list'] as &$item) {
                $item['attach_data'] = json_decode($item['attach'], true);
            }
        }

        return [
            'total_count' => $result['total'],
            'page' => $result['page'],
            'page_size' => $pageSize,
            'total_pages' => ceil($result['total'] / $pageSize),
            'list' => $result['list']
        ];
    }

    /**
     * 更新发货建议状态
     * @param int $id 建议ID
     * @param string $status 新状态
     * @return bool
     */
    public function updateSuggestionStatus($id, $status)
    {
        $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
        if (!in_array($status, $allowedStatus)) {
            return false;
        }

        return $this->dbLogistics->table('shipping_suggestion')
            ->where('id = :id', ['id' => $id])
            ->update(['status' => $status, 'updated_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * 批量更新发货建议状态
     * @param array $ids 建议ID数组
     * @param string $status 新状态
     * @return bool
     */
    public function batchUpdateStatus($ids, $status)
    {
        if (empty($ids)) {
            return false;
        }

        $allowedStatus = ['pending', 'processing', 'completed', 'ignored'];
        if (!in_array($status, $allowedStatus)) {
            return false;
        }

        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE shipping_suggestion
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id IN ($placeholders)";

        $params = array_merge([$status], $ids);
        return $this->dbLogistics->execute($sql, $params);
    }

    /**
     * 获取发货建议详情
     * @param int $id 建议ID
     * @return array|false
     */
    public function getSuggestionDetail($id)
    {
        $suggestion = $this->dbLogistics->table('shipping_suggestion')
            ->where('id = :id', ['id' => $id])
            ->one();

        if ($suggestion) {
            $suggestion['attach_data'] = json_decode($suggestion['attach'], true);
        }

        return $suggestion;
    }

    /**
     * 删除过期的发货建议
     * @param int $days 保留天数
     * @return int 删除数量
     */
    public function cleanExpiredSuggestions($days = 30)
    {
        $sql = "DELETE FROM shipping_suggestion
                WHERE suggestion_date < DATE_SUB(CURDATE(), INTERVAL :days DAY)
                AND status IN ('completed', 'ignored')";

        $result = $this->dbLogistics->execute($sql, ['days' => $days]);
        return $this->dbLogistics->rowCount();
    }

    /**
     * 获取发货建议统计
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getSuggestionStatistics($startDate, $endDate)
    {
        $sql = "SELECT
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_count,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                    SUM(CASE WHEN status = 'ignored' THEN 1 ELSE 0 END) as ignored_count,
                    SUM(CASE WHEN urgent_flag = 1 THEN 1 ELSE 0 END) as urgent_count,
                    SUM(suggested_stock) as total_suggested_stock,
                    AVG(suggested_stock) as avg_suggested_stock
                FROM shipping_suggestion
                WHERE suggestion_date BETWEEN :start_date AND :end_date";

        return $this->dbLogistics->query($sql, [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]) ?: [];
    }
}
