<?php
/**
 * 预警配置模型
 * @purpose 管理库存预警的配置规则和阈值参数
 * @Author: System
 * @Time: 2025/07/07
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbFMysql;
use core\lib\log;

class warningConfigModel
{
    private $db;
    private $fDb;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
        $this->fDb = dbFMysql::getInstance();
    }

    /**
     * 获取安全库存配置
     * @param string $asin
     * @param string $sku
     * @param string $siteCode
     * @return array
     */
    public function getSafetyStockConfig($asin, $sku = '', $siteCode = '')
    {
        // 这里应该根据实际的安全库存配置表来查询
        // 暂时返回默认配置
        return [
            'safety_stock_min' => 100,
            'safety_stock_max' => 1000
        ];
    }

    /**
     * 获取店铺流水预警阈值配置
     * @return array
     */
    public function getStoreTurnoverThresholds()
    {
        // 从配置表或配置文件中获取流水预警阈值
        return [
            'main_backup' => 50000,      // 主备货号 > $50,000
            'secondary_backup' => 30000,  // 次备货号 > $30,000
            'fk_review' => 20000,        // FK回评号 > $20,000
            'brush' => 20000             // 刷单号 > $20,000
        ];
    }

    /**
     * 获取备货号占比配置
     * @param int $productStage 产品阶段
     * @return array
     */
    public function getBackupRatioConfig($productStage)
    {
        $configs = [
            4 => ['min' => 15, 'max' => 25], // 新品期：15%~25%
            1 => ['min' => 15, 'max' => 25], // 成长期：15%~25%
            2 => ['min' => 55, 'max' => 65], // 稳定期：55%~65%
            3 => ['min' => 55, 'max' => 65], // 衰退期：55%~65%
            5 => ['min' => 0, 'max' => 100]  // 清货：不限制
        ];
        
        return $configs[$productStage] ?? ['min' => 0, 'max' => 100];
    }

    /**
     * 获取风险店铺库存上限配置
     * @param int $sid 店铺ID
     * @return array
     */
    public function getRiskStoreConfig($sid)
    {
        // 这里应该根据实际的风险店铺配置表来查询
        // 暂时返回默认配置
        return [
            'inventory_limit' => 10000,
            'is_risk_store' => false
        ];
    }

    /**
     * 获取销量计算配置
     * @param int $productStage 产品阶段
     * @return array
     */
    public function getSalesCalculationConfig($productStage)
    {
        // 根据产品阶段获取销量计算权重配置
        $configs = [
            1 => ['7day' => 0.6, '14day' => 0.2, '30day' => 0.2], // 成长期
            2 => ['7day' => 0.5, '14day' => 0.3, '30day' => 0.2], // 稳定期
            3 => ['7day' => 0.4, '14day' => 0.3, '30day' => 0.3], // 衰退期
            4 => ['7day' => 0.7, '14day' => 0.2, '30day' => 0.1], // 新品期
            5 => ['7day' => 0.3, '14day' => 0.3, '30day' => 0.4]  // 清货
        ];
        
        return $configs[$productStage] ?? ['7day' => 0.5, '14day' => 0.3, '30day' => 0.2];
    }

    /**
     * 保存安全库存配置
     * @param array $config
     * @return bool
     */
    public function saveSafetyStockConfig($config)
    {
        try {
            // 这里应该保存到实际的配置表中
            // 暂时记录日志
            log::lingXingApi('WarningConfig')->info('保存安全库存配置', $config);
            return true;
        } catch (\Exception $e) {
            log::lingXingApi('WarningConfig')->error('保存安全库存配置失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 保存流水预警阈值配置
     * @param array $thresholds
     * @return bool
     */
    public function saveStoreTurnoverThresholds($thresholds)
    {
        try {
            // 这里应该保存到实际的配置表中
            // 暂时记录日志
            log::lingXingApi('WarningConfig')->info('保存流水预警阈值配置', $thresholds);
            return true;
        } catch (\Exception $e) {
            log::lingXingApi('WarningConfig')->error('保存流水预警阈值配置失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取预警规则列表
     * @param array $params
     * @return array
     */
    public function getWarningRules($params = [])
    {
        $query = $this->fDb->table('waring_rules')
            ->where('is_delete = 0');
        
        if (!empty($params['status'])) {
            $query->andWhere('status = :status', ['status' => $params['status']]);
        }
        
        if (!empty($params['waring_name'])) {
            $query->andWhere('waring_name LIKE :waring_name', ['waring_name' => '%' . $params['waring_name'] . '%']);
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        
        return $query->order('id DESC')
            ->pages($page, $pageSize);
    }

    /**
     * 获取预警规则详情
     * @param int $id
     * @return array|null
     */
    public function getWarningRuleDetail($id)
    {
        $rule = $this->fDb->table('waring_rules')
            ->where('id = :id AND is_delete = 0', ['id' => $id])
            ->one();
            
        if ($rule) {
            $rule['rules'] = json_decode($rule['rules'], true);
            $rule['receive_type'] = json_decode($rule['receive_type'], true);
        }
        
        return $rule;
    }

    /**
     * 创建或更新预警规则
     * @param array $data
     * @return int|bool
     */
    public function saveWarningRule($data)
    {
        try {
            $this->fDb->beginTransaction();
            
            $saveData = [
                'waring_name' => $data['waring_name'],
                'status' => $data['status'],
                'level' => $data['level'],
                'dimension' => $data['dimension'],
                'begin_time' => strtotime($data['begin_time']),
                'receive_type' => json_encode($data['receive_type']),
                'rules' => json_encode($data['rules']),
                'column_id' => $data['column_id'],
                'description' => $data['description'] ?? '',
                'updated_time' => date('Y-m-d H:i:s')
            ];
            
            if (!empty($data['id'])) {
                // 更新
                $result = $this->fDb->table('waring_rules')
                    ->where('id = :id', ['id' => $data['id']])
                    ->update($saveData);
                $this->fDb->commit();
                return $result;
            } else {
                // 新增
                $saveData['created_time'] = date('Y-m-d H:i:s');
                $saveData['user_id'] = $data['user_id'] ?? 0;
                $saveData['updated_user_id'] = $data['user_id'] ?? 0;
                
                $result = $this->fDb->table('waring_rules')->insert($saveData);
                $this->fDb->commit();
                return $result;
            }
        } catch (\Exception $e) {
            $this->fDb->rollBack();
            log::lingXingApi('WarningConfig')->error('保存预警规则失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除预警规则
     * @param int $id
     * @return bool
     */
    public function deleteWarningRule($id)
    {
        try {
            $result = $this->fDb->table('waring_rules')
                ->where('id = :id', ['id' => $id])
                ->update(['is_delete' => 1, 'updated_time' => date('Y-m-d H:i:s')]);
            return $result > 0;
        } catch (\Exception $e) {
            log::lingXingApi('WarningConfig')->error('删除预警规则失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取监控指标列表
     * @return array
     */
    public function getMonitoringColumns()
    {
        return $this->fDb->table('column')
            ->where('is_delete = 0 AND is_monitoring = 1')
            ->field('id, column_name, show_name, show_type')
            ->list();
    }

    /**
     * 验证预警规则
     * @param array $rules
     * @param string $showType
     * @return array
     */
    public function validateWarningRules($rules, $showType)
    {
        // 这里实现预警规则的验证逻辑
        // 根据字段类型验证规则格式
        $validatedRules = [];
        
        foreach ($rules as $rule) {
            if ($this->isValidRule($rule, $showType)) {
                $validatedRules[] = $rule;
            }
        }
        
        return $validatedRules;
    }

    /**
     * 验证单个规则
     * @param array $rule
     * @param string $showType
     * @return bool
     */
    private function isValidRule($rule, $showType)
    {
        // 根据字段类型验证规则
        if (empty($rule['operator']) || empty($rule['value'])) {
            return false;
        }
        
        // 数值类型验证
        if (in_array($showType, ['number', 'decimal']) && !is_numeric($rule['value'])) {
            return false;
        }
        
        return true;
    }
}
