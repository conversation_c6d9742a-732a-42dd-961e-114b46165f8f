#!/bin/bash

# 每日备货建议更新脚本
# 用于自动执行备货建议计算和FBA冗余标记更新
# 建议每天早上8点执行

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
LOG_DIR="$PROJECT_ROOT/logs/stock_suggestion"
LOG_FILE="$LOG_DIR/daily_update_$(date +%Y%m%d).log"
API_URL="http://localhost/oa-api/task/index.php"
TOKEN="01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    log "ERROR: $1"
    exit 1
}

# 发送API请求函数
send_request() {
    local method="$1"
    local data="$2"
    
    log "发送请求: $method"
    
    response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "method=$method&token=$TOKEN&$data" \
        --max-time 300)
    
    if [ $? -ne 0 ]; then
        handle_error "API请求失败: $method"
    fi
    
    echo "$response"
}

# 检查API响应
check_response() {
    local response="$1"
    local method="$2"
    
    # 检查是否包含成功标识
    if echo "$response" | grep -q '"code":1\|"success":true'; then
        log "SUCCESS: $method 执行成功"
        return 0
    else
        log "ERROR: $method 执行失败"
        log "Response: $response"
        return 1
    fi
}

# 主执行函数
main() {
    log "========================================="
    log "开始执行每日备货建议更新任务"
    log "========================================="
    
    # 获取当前日期
    current_date=$(date +%Y-%m-%d)
    log "计算日期: $current_date"
    
    # 1. 执行每日备货建议更新
    log "步骤1: 执行每日备货建议更新"
    response=$(send_request "dailyStockSuggestionUpdate" "calculation_date=$current_date")
    
    if ! check_response "$response" "dailyStockSuggestionUpdate"; then
        handle_error "每日备货建议更新失败"
    fi
    
    # 解析响应获取统计信息
    current_week_processed=$(echo "$response" | grep -o '"processed_count":[0-9]*' | cut -d':' -f2 | head -1)
    current_week_suggested=$(echo "$response" | grep -o '"suggested_count":[0-9]*' | cut -d':' -f2 | head -1)
    current_week_redundant=$(echo "$response" | grep -o '"redundant_count":[0-9]*' | cut -d':' -f2 | head -1)
    
    log "本周备货建议统计:"
    log "  - 处理产品数: ${current_week_processed:-0}"
    log "  - 需要备货: ${current_week_suggested:-0}"
    log "  - 库存冗余: ${current_week_redundant:-0}"
    
    # 2. 等待一段时间，避免数据库压力
    log "等待5秒..."
    sleep 5
    
    # 3. 获取备货建议统计
    log "步骤2: 获取备货建议统计"
    response=$(send_request "getFbaRedundantStatistics" "")
    
    if check_response "$response" "getFbaRedundantStatistics"; then
        log "FBA冗余统计获取成功"
    else
        log "WARNING: FBA冗余统计获取失败，但不影响主流程"
    fi
    
    log "========================================="
    log "每日备货建议更新任务完成"
    log "========================================="
    
    # 4. 清理旧日志文件（保留30天）
    find "$LOG_DIR" -name "daily_update_*.log" -mtime +30 -delete 2>/dev/null
    
    log "任务执行完成，日志已保存到: $LOG_FILE"
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    # 检查是否已有实例在运行
    LOCK_FILE="/tmp/daily_stock_suggestion.lock"
    
    if [ -f "$LOCK_FILE" ]; then
        PID=$(cat "$LOCK_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log "ERROR: 脚本已在运行中 (PID: $PID)"
            exit 1
        else
            log "WARNING: 发现僵尸锁文件，清理中..."
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # 创建锁文件
    echo $$ > "$LOCK_FILE"
    
    # 设置退出时清理锁文件
    trap 'rm -f "$LOCK_FILE"' EXIT
    
    # 执行主函数
    main "$@"
fi
