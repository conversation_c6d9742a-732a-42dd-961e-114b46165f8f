#!/bin/bash
token="01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0"

# 企微和飞书机器人配置
QW_URL='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336'
FS_URL='https://open.feishu.cn/open-apis/bot/v2/hook/2b63c602-bb10-465f-bc9a-af4dc1f02233'

# 执行备货建议
echo "开始执行备货建议生成..."
start_time=$(date +"%Y-%m-%d %H:%M:%S")

while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    response=$(curl -s -X POST -d "token=$token" "http://oa.ywx.com/task/logistics/dailyStockSuggestion")
    echo "$response"

    # 从汇总响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    echo "code=$code"
    # 从汇总响应中提取msg字段的值
    msg=$(echo "$response" | grep -oP '"message"\s*:\s*"\K[^"]+')

    end_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done

