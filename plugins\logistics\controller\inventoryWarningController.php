<?php
/**
 * 库存预警控制器
 * @purpose 提供库存预警的API接口，包括概览和明细汇总
 * @Author: System
 * @Time: 2025/07/07
 */

namespace plugins\logistics\controller;

use plugins\logistics\models\inventoryWarningModel;

class inventoryWarningController extends baseController
{
    private $model;

    public function __construct()
    {
        $this->model = new inventoryWarningModel();
        parent::__construct();
    }

    /**
     * 获取库存预警概览
     * GET /plugins/logistics/inventoryWarning/overview
     * @return void
     */
    public function getOverview()
    {
        try {
            $result = $this->model->getWarningOverview();
            returnSuccess($result);
        } catch (\Exception $e) {
            returnError('获取库存预警概览失败：' . $e->getMessage());
        }
    }

    /**
     * 获取库存明细汇总列表
     * GET /plugins/logistics/inventoryWarning/detailList
     * @return void
     */
    public function getDetailList()
    {
        try {
            // 获取查询参数
            $params = $this->validateDetailParams($_GET);
            
            // 获取数据
            $result = $this->model->getInventoryDetailList($params);
            
            returnSuccess($result);
        } catch (\Exception $e) {
            returnError('获取库存明细汇总失败：' . $e->getMessage());
        }
    }

    /**
     * 验证明细列表查询参数
     * @param array $params
     * @return array
     */
    private function validateDetailParams($params)
    {
        $validated = [];
        
        // 搜索条件
        if (!empty($params['asin'])) {
            $validated['asin'] = trim($params['asin']);
        }
        
        if (!empty($params['sku'])) {
            $validated['sku'] = trim($params['sku']);
        }
        
        if (!empty($params['product_name'])) {
            $validated['product_name'] = trim($params['product_name']);
        }
        
        // 筛选条件
        if (!empty($params['site_code'])) {
            $validated['site_code'] = trim($params['site_code']);
        }
        
        if (!empty($params['category_name'])) {
            $validated['category_name'] = trim($params['category_name']);
        }
        
        if (!empty($params['product_stage']) && is_numeric($params['product_stage'])) {
            $validated['product_stage'] = (int)$params['product_stage'];
        }
        
        if (!empty($params['operation_group'])) {
            $validated['operation_group'] = trim($params['operation_group']);
        }
        
        // 显示配置
        if (isset($params['show_group'])) {
            $validated['show_group'] = (int)$params['show_group'];
        }
        
        if (isset($params['show_site'])) {
            $validated['show_site'] = (int)$params['show_site'];
        }
        
        // 分页参数
        $validated['page'] = max(1, (int)($params['page'] ?? 1));
        $validated['page_size'] = min(100, max(1, (int)($params['page_size'] ?? 20)));
        
        return $validated;
    }

    /**
     * 批量生成回货需求
     * POST /plugins/logistics/inventoryWarning/generateReplenishment
     * @return void
     */
    public function generateReplenishment()
    {
        try {
            $postData = $_POST;
            
            if (empty($postData['product_ids']) || !is_array($postData['product_ids'])) {
                returnError('请选择要生成回货需求的产品');
            }
            
            // 这里实现批量生成回货需求的逻辑
            // 暂时返回成功消息
            $successCount = count($postData['product_ids']);
            
            returnSuccess([
                'message' => "成功生成 {$successCount} 个产品的回货需求",
                'success_count' => $successCount
            ]);
            
        } catch (\Exception $e) {
            returnError('生成回货需求失败：' . $e->getMessage());
        }
    }

    /**
     * 导出库存明细数据
     * POST /plugins/logistics/inventoryWarning/export
     * @return void
     */
    public function exportDetailList()
    {
        try {
            // 获取查询参数
            $params = $this->validateDetailParams($_POST);
            
            // 设置大页面大小用于导出
            $params['page'] = 1;
            $params['page_size'] = 10000;
            
            // 获取数据
            $result = $this->model->getInventoryDetailList($params);
            
            if (empty($result['list'])) {
                returnError('没有可导出的数据');
            }
            
            // 格式化导出数据
            $exportData = $this->formatExportData($result['list']);
            
            returnSuccess([
                'data' => $exportData,
                'total' => count($exportData),
                'filename' => '库存预警明细_' . date('Y-m-d_H-i-s') . '.xlsx'
            ]);
            
        } catch (\Exception $e) {
            returnError('导出数据失败：' . $e->getMessage());
        }
    }

    /**
     * 格式化导出数据
     * @param array $list
     * @return array
     */
    private function formatExportData($list)
    {
        $exportData = [];
        
        foreach ($list as $item) {
            $exportData[] = [
                'ASIN' => $item['asin'],
                'SKU' => $item['sku'],
                '品名' => $item['product_name'] ?? '',
                '站点' => $item['site_code'] ?? '',
                '分类' => $item['category_name'] ?? '',
                'Listing阶段' => $item['product_stage_text'] ?? '',
                '备货定位' => $item['stock_positioning_text'] ?? '',
                'FBA可售' => $item['fba_sellable_qty'],
                'FBA在途' => $item['fba_inbound_qty'],
                'FBA可售库存' => $item['fba_sellable_qty'] + $item['fba_inbound_qty'],
                '海外仓库存' => $item['overseas_warehouse_qty'],
                '海外仓在途' => $item['overseas_warehouse_inbound_qty'],
                '可用库存' => $item['fba_sellable_qty'] + $item['fba_inbound_qty'] + $item['overseas_warehouse_qty'] + $item['overseas_warehouse_inbound_qty'],
                '日均销量' => $item['daily_avg_sales_qty'],
                'FBA预计可售天数' => $item['fba_sellable_days'],
                '可用预计可售天数' => $item['available_sellable_days'],
                '安全库存下限' => $item['safety_stock_min'] ?? '',
                '安全库存上限' => $item['safety_stock_max'] ?? '',
                'FBA预警状态' => $item['fba_warning']['text'] ?? '',
                '可用库存预警状态' => $item['available_warning']['text'] ?? '',
                '产品标识' => implode(',', $item['product_tags'] ?? [])
            ];
        }
        
        return $exportData;
    }

    /**
     * 获取配置选项
     * GET /plugins/logistics/inventoryWarning/config
     * @return void
     */
    public function getConfig()
    {
        try {
            $config = [
                'product_stages' => [
                    ['value' => 1, 'label' => '成长期'],
                    ['value' => 2, 'label' => '稳定期'],
                    ['value' => 3, 'label' => '衰退期'],
                    ['value' => 4, 'label' => '新品期'],
                    ['value' => 5, 'label' => '清货']
                ],
                'stock_positioning' => [
                    ['value' => 1, 'label' => '重点备货'],
                    ['value' => 2, 'label' => '常规备货'],
                    ['value' => 3, 'label' => '停止备货']
                ],
                'product_positioning' => [
                    ['value' => 1, 'label' => '头部产品'],
                    ['value' => 2, 'label' => '腰部产品'],
                    ['value' => 3, 'label' => '尾部产品'],
                    ['value' => 4, 'label' => '清货产品']
                ]
            ];
            
            returnSuccess($config);
        } catch (\Exception $e) {
            returnError('获取配置失败：' . $e->getMessage());
        }
    }
}
