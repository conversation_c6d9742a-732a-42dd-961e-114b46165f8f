#!/bin/bash

# 每日采购建议更新脚本
# 用于自动执行采购建议计算和状态管理
# 建议每天早上9点执行（在备货建议之后）

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
LOG_DIR="$PROJECT_ROOT/logs/purchase_suggestion"
LOG_FILE="$LOG_DIR/daily_update_$(date +%Y%m%d).log"
API_URL="http://localhost/oa-api/task/index.php"
TOKEN="01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    log "ERROR: $1"
    exit 1
}

# 发送API请求函数
send_request() {
    local method="$1"
    local data="$2"
    
    log "发送请求: $method"
    
    response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "method=$method&token=$TOKEN&$data" \
        --max-time 300)
    
    if [ $? -ne 0 ]; then
        handle_error "API请求失败: $method"
    fi
    
    echo "$response"
}

# 检查API响应
check_response() {
    local response="$1"
    local method="$2"
    
    # 检查是否包含成功标识
    if echo "$response" | grep -q '"code":1\|"success":true'; then
        log "SUCCESS: $method 执行成功"
        return 0
    else
        log "ERROR: $method 执行失败"
        log "Response: $response"
        return 1
    fi
}

# 检查是否是周一
is_monday() {
    local day_of_week=$(date +%u) # 1=周一, 7=周日
    [ "$day_of_week" -eq 1 ]
}

# 主执行函数
main() {
    log "========================================="
    log "开始执行每日采购建议更新任务"
    log "========================================="
    
    # 获取当前日期
    current_date=$(date +%Y-%m-%d)
    log "计算日期: $current_date"
    
    # 检查是否是周一
    if is_monday; then
        log "今天是周一，将执行周数据清理"
    fi
    
    # 1. 执行每日采购建议更新
    log "步骤1: 执行每日采购建议更新"
    response=$(send_request "dailyPurchaseSuggestionUpdate" "calculation_date=$current_date")
    
    if ! check_response "$response" "dailyPurchaseSuggestionUpdate"; then
        handle_error "每日采购建议更新失败"
    fi
    
    # 解析响应获取统计信息
    current_week_processed=$(echo "$response" | grep -o '"processed_count":[0-9]*' | cut -d':' -f2 | head -1)
    current_week_suggested=$(echo "$response" | grep -o '"suggested_count":[0-9]*' | cut -d':' -f2 | head -1)
    current_week_redundant=$(echo "$response" | grep -o '"redundant_count":[0-9]*' | cut -d':' -f2 | head -1)
    
    log "本周采购建议统计:"
    log "  - 处理SKU数: ${current_week_processed:-0}"
    log "  - 需要采购: ${current_week_suggested:-0}"
    log "  - 采购冗余: ${current_week_redundant:-0}"
    
    # 2. 等待一段时间，避免数据库压力
    log "等待3秒..."
    sleep 3
    
    # 3. 获取采购建议统计（可选）
    log "步骤2: 获取采购建议统计"
    response=$(send_request "getPurchaseSuggestionList" "page=1&page_size=1")
    
    if check_response "$response" "getPurchaseSuggestionList"; then
        log "采购建议统计获取成功"
        
        # 解析总数
        total_count=$(echo "$response" | grep -o '"total_count":[0-9]*' | cut -d':' -f2)
        log "当前采购建议总数: ${total_count:-0}"
    else
        log "WARNING: 采购建议统计获取失败，但不影响主流程"
    fi
    
    log "========================================="
    log "每日采购建议更新任务完成"
    log "========================================="
    
    # 4. 清理旧日志文件（保留30天）
    find "$LOG_DIR" -name "daily_update_*.log" -mtime +30 -delete 2>/dev/null
    
    log "任务执行完成，日志已保存到: $LOG_FILE"
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    # 检查是否已有实例在运行
    LOCK_FILE="/tmp/daily_purchase_suggestion.lock"
    
    if [ -f "$LOCK_FILE" ]; then
        PID=$(cat "$LOCK_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log "ERROR: 脚本已在运行中 (PID: $PID)"
            exit 1
        else
            log "WARNING: 发现僵尸锁文件，清理中..."
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # 创建锁文件
    echo $$ > "$LOCK_FILE"
    
    # 设置退出时清理锁文件
    trap 'rm -f "$LOCK_FILE"' EXIT
    
    # 执行主函数
    main "$@"
fi
