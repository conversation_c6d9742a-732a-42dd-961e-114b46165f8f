<?php
namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;


/**
 * 备货建议模型
 * 实现完整的备货建议计算逻辑
 */
class StockSuggestionModel
{
    private $dbErp;
    private $dbLogistics;

    public function __construct()
    {
        $this->dbErp = dbErpMysql::getInstance();
        $this->dbLogistics = dbLMysql::getInstance();
    }

    /**
     * 生成指定周期的备货建议
     * 
     * @param string $weekType 'current'|'next'
     * @param string $calculationDate 计算日期 Y-m-d
     * @return array
     */
    public function generateWeeklyStockSuggestion($weekType, $calculationDate = null)
    {
        if (!$calculationDate) {
            $calculationDate = date('Y-m-d');
        }

        try {
            $this->dbLogistics->beginTransaction();

            // 1. 获取所有需要计算的产品
            $products = $this->getProductsForCalculation();
            
            $processedCount = 0;
            $suggestedCount = 0;
            $redundantCount = 0;

            foreach ($products as $product) {
                // 检查是否被忽略，如果被忽略则跳过
                if ($this->isProductIgnored($product['asin'], $product['country'], $calculationDate)) {
                    continue;
                }

                $result = $this->calculateSingleProductSuggestion(
                    $product['asin'],
                    $product['country'],
                    $weekType,
                    $calculationDate
                );

                if ($result) {
                    $this->saveSuggestionResult($result);
                    $processedCount++;

                    if ($result['suggestion_action'] === 'suggest') {
                        $suggestedCount++;
                    } elseif ($result['suggestion_action'] === 'redundant') {
                        $redundantCount++;
                    }
                }
            }

            $this->dbLogistics->commit();

            return [
                'success' => true,
                'week_type' => $weekType,
                'calculation_date' => $calculationDate,
                'processed_count' => $processedCount,
                'suggested_count' => $suggestedCount,
                'redundant_count' => $redundantCount
            ];

        } catch (Exception $e) {
            $this->dbLogistics->rollBack();
            throw $e;
        }
    }

    /**
     * 计算单个产品的备货建议
     * 
     * @param string $asin
     * @param string $country
     * @param string $weekType
     * @param string $calculationDate
     * @return array|null
     */
    public function calculateSingleProductSuggestion($asin, $country, $weekType, $calculationDate)
    {
        // 1. 获取产品基础数据
        $productData = $this->getProductData($asin, $country);
        if (!$productData) {
            return null;
        }

        // 2. 获取销量数据
        $salesData = $this->getSalesData($asin, $country);
        if (!$salesData) {
            return null;
        }

        // 3. 获取配置数据
        $config = $this->getNormalPrepareConfig($productData['product_stage'], $country);
        if (!$config) {
            return null;
        }

        // 4. 计算预估日销量
        $estimatedDailySales = $this->calculateEstimatedDailySales($salesData, $config['sales_config']);

        // 5. 计算缓冲天数
        $bufferDays = TimeCalculator::calculateBufferDays($calculationDate, $weekType, $country);
        $totalNeedDays = $config['data_range']['normal_value'] + $bufferDays;

        // 6. 获取节日配置
        $festivalConfigs = $this->getFestivalConfigs($country);

        // 7. 计算节日影响下的总需求量
        $suggestionResult = $this->calculateFestivalStockSuggestion(
            $calculationDate,
            $totalNeedDays,
            $estimatedDailySales,
            $festivalConfigs,
            $country
        );

        // 8. 计算库存阈值
        $thresholds = $this->calculateThresholds($config['data_range'], $bufferDays, $estimatedDailySales);

        // 9. 判断库存状态
        $currentStock = $productData['fba_sellable_qty'] + $productData['overseas_warehouse_qty'];
        $stockStatus = $this->determineStockStatus($currentStock, $thresholds);

        // 10. 计算最终建议数量
        $finalSuggestionQty = 0;
        if ($stockStatus['action'] === 'suggest') {
            $finalSuggestionQty = max(0, $suggestionResult['total_suggestion_qty'] - $currentStock);
        }

        // 11. 获取发货日期
        $shippingDate = TimeCalculator::getShippingDate($calculationDate, $weekType, $country);

        return [
            'asin' => $asin,
            'sku' => $productData['sku'],
            'country' => $country,
            'product_stage' => $productData['product_stage'],
            'suggestion_week' => $weekType,
            'current_fba_stock' => $productData['fba_sellable_qty'],
            'current_overseas_stock' => $productData['overseas_warehouse_qty'],
            'total_available_stock' => $currentStock,
            'sales_7_avg' => $salesData['sales_7_avg'],
            'sales_14_avg' => $salesData['sales_14_avg'],
            'sales_30_avg' => $salesData['sales_30_avg'],
            'estimated_daily_sales' => $estimatedDailySales,
            'safety_min_days' => $config['data_range']['min_value'],
            'safety_max_days' => $config['data_range']['max_value'],
            'safety_normal_days' => $config['data_range']['normal_value'],
            'buffer_days' => $bufferDays,
            'total_need_days' => $totalNeedDays,
            'min_threshold' => $thresholds['min'],
            'max_threshold' => $thresholds['max'],
            'normal_threshold' => $thresholds['normal'],
            'base_suggestion_qty' => $suggestionResult['base_suggestion_qty'],
            'festival_suggestion_qty' => $suggestionResult['festival_suggestion_qty'],
            'total_suggestion_qty' => $suggestionResult['total_suggestion_qty'],
            'final_suggestion_qty' => $finalSuggestionQty,
            'suggestion_action' => $stockStatus['action'],
            'suggestion_reason' => $stockStatus['reason'],
            'festival_configs' => json_encode($suggestionResult['festival_configs']),
            'has_festival_impact' => $suggestionResult['has_festival_impact'],
            'calculation_date' => $calculationDate,
            'shipping_date' => $shippingDate
        ];
    }

    /**
     * 获取需要计算的产品列表
     */
    private function getProductsForCalculation()
    {
        return $this->dbErp->table('fba_storage_summary')
            ->field('DISTINCT asin, country')
            ->where('fba_sellable_qty > 0 OR overseas_warehouse_qty > 0')
            ->order('asin, country')
            ->list();
    }

    /**
     * 获取产品基础数据
     */
    private function getProductData($asin, $country)
    {
        // 由于需要使用子查询，这里仍然使用原生SQL
        $sql = "SELECT asin, sku, country, product_stage,
                       fba_sellable_qty,
                       COALESCE((SELECT SUM(qty) FROM overseas_warehouse_inventory
                                WHERE asin = f.asin AND country = f.country AND level_type = 2), 0) as overseas_warehouse_qty
                FROM fba_storage_summary f
                WHERE asin = :asin AND country = :country";

        return $this->dbErp->query($sql, [':asin' => $asin, ':country' => $country]);
    }

    /**
     * 获取销量数据
     */
    private function getSalesData($asin, $country)
    {
        // 这里需要根据实际的销量数据表结构来实现
        // 暂时返回模拟数据
        return [
            'sales_7_avg' => 10.5,
            'sales_14_avg' => 12.3,
            'sales_30_avg' => 11.8
        ];
    }

    /**
     * 获取normal_prepare配置
     */
    private function getNormalPrepareConfig($productStage, $country)
    {
        $configData = $this->dbErp->table('config')
            ->field('config_value')
            ->where('config_key = :config_key', [':config_key' => 'normal_prepare'])
            ->one();

        if (!$configData) {
            return null;
        }

        $configData = $configData['config_value'];

        $configs = json_decode($configData, true);
        foreach ($configs as $config) {
            if ($config['type'] == $productStage) {
                // 查找对应国家的配置
                foreach ($config['data_range'] as $range) {
                    if ($range['country'] === $country) {
                        return [
                            'sales_config' => $config['sales_config'],
                            'data_range' => $range
                        ];
                    }
                }
            }
        }

        return null;
    }

    /**
     * 计算预估日销量
     */
    private function calculateEstimatedDailySales($salesData, $salesConfig)
    {
        $sales7Weight = $salesConfig['sales_7'] / 100;
        $sales14Weight = $salesConfig['sales_14'] / 100;
        $sales30Weight = $salesConfig['sales_30'] / 100;

        return $salesData['sales_7_avg'] * $sales7Weight +
               $salesData['sales_14_avg'] * $sales14Weight +
               $salesData['sales_30_avg'] * $sales30Weight;
    }

    /**
     * 获取节日配置
     */
    private function getFestivalConfigs($country)
    {
        $configData = $this->dbErp->table('config')
            ->field('config_value')
            ->where('config_key = :config_key', [':config_key' => 'festival_activity'])
            ->one();

        if (!$configData) {
            return [];
        }

        $configData = $configData['config_value'];

        $festivals = json_decode($configData, true);
        $applicableFestivals = [];

        foreach ($festivals as $festival) {
            if (in_array($country, $festival['sites_config'])) {
                $applicableFestivals[] = $festival;
            }
        }

        return $applicableFestivals;
    }

    /**
     * 计算节日影响下的备货建议
     */
    private function calculateFestivalStockSuggestion($startDate, $totalDays, $dailySales, $festivalConfigs, $country)
    {
        $baseSuggestion = $totalDays * $dailySales;
        $festivalSuggestion = 0;
        $totalSuggestion = 0;
        $hasFestivalImpact = false;
        $affectedFestivals = [];

        for ($i = 0; $i < $totalDays; $i++) {
            $currentDate = date('Y-m-d', strtotime($startDate . " +{$i} days"));
            $dayOfYear = TimeCalculator::getMonthDayFormat($currentDate);

            $dayMultiplier = 1.0; // 基础100%
            $maxFestivalMultiplier = TimeCalculator::getMaxFestivalMultiplier($dayOfYear, $festivalConfigs, $country);

            if ($maxFestivalMultiplier > 0) {
                $hasFestivalImpact = true;
                $festivalSuggestion += $dailySales * $maxFestivalMultiplier;

                // 记录影响的节日
                foreach ($festivalConfigs as $festival) {
                    if (in_array($country, $festival['sites_config'])) {
                        $isInFestival = TimeCalculator::isInFestivalPeriod($dayOfYear, $festival['period']);
                        $isInAdvance = TimeCalculator::isInAdvancePeriod($dayOfYear, $festival['period'], $festival['stock_rules']['day_before']);

                        if ($isInFestival || $isInAdvance) {
                            $affectedFestivals[] = $festival['name'];
                        }
                    }
                }
            }

            // 当天总倍数 = 常规100% + 最高节日倍数
            $totalMultiplier = $dayMultiplier + $maxFestivalMultiplier;
            $totalSuggestion += $dailySales * $totalMultiplier;
        }

        return [
            'base_suggestion_qty' => $baseSuggestion,
            'festival_suggestion_qty' => $festivalSuggestion,
            'total_suggestion_qty' => $totalSuggestion,
            'has_festival_impact' => $hasFestivalImpact,
            'festival_configs' => array_unique($affectedFestivals)
        ];
    }

    /**
     * 计算库存阈值
     */
    private function calculateThresholds($dataRange, $bufferDays, $estimatedDailySales)
    {
        return [
            'min' => ($dataRange['min_value'] + $bufferDays) * $estimatedDailySales,
            'max' => ($dataRange['max_value'] + $bufferDays) * $estimatedDailySales,
            'normal' => ($dataRange['normal_value'] + $bufferDays) * $estimatedDailySales
        ];
    }

    /**
     * 判断库存状态
     */
    private function determineStockStatus($currentStock, $thresholds)
    {
        if ($currentStock < $thresholds['min']) {
            return [
                'action' => 'suggest',
                'reason' => '当前库存低于安全库存最小值，需要备货'
            ];
        } elseif ($currentStock > $thresholds['max']) {
            return [
                'action' => 'redundant',
                'reason' => '当前库存超过安全库存最大值，库存冗余'
            ];
        } else {
            return [
                'action' => 'normal',
                'reason' => '当前库存在安全范围内'
            ];
        }
    }

    /**
     * 保存备货建议结果
     */
    private function saveSuggestionResult($result)
    {
        // 由于需要使用ON DUPLICATE KEY UPDATE，这里仍然使用原生SQL
        // 这是一个复杂的UPSERT操作，dbLMysql没有直接封装此功能
        $sql = "INSERT INTO stock_suggestion (
                    asin, sku, country, product_stage, suggestion_week,
                    current_fba_stock, current_overseas_stock, total_available_stock,
                    sales_7_avg, sales_14_avg, sales_30_avg, estimated_daily_sales,
                    safety_min_days, safety_max_days, safety_normal_days, buffer_days, total_need_days,
                    min_threshold, max_threshold, normal_threshold,
                    base_suggestion_qty, festival_suggestion_qty, total_suggestion_qty,
                    final_suggestion_qty, suggestion_action, suggestion_reason,
                    festival_configs, has_festival_impact, calculation_date, shipping_date
                ) VALUES (
                    :asin, :sku, :country, :product_stage, :suggestion_week,
                    :current_fba_stock, :current_overseas_stock, :total_available_stock,
                    :sales_7_avg, :sales_14_avg, :sales_30_avg, :estimated_daily_sales,
                    :safety_min_days, :safety_max_days, :safety_normal_days, :buffer_days, :total_need_days,
                    :min_threshold, :max_threshold, :normal_threshold,
                    :base_suggestion_qty, :festival_suggestion_qty, :total_suggestion_qty,
                    :final_suggestion_qty, :suggestion_action, :suggestion_reason,
                    :festival_configs, :has_festival_impact, :calculation_date, :shipping_date
                ) ON DUPLICATE KEY UPDATE
                    current_fba_stock = VALUES(current_fba_stock),
                    current_overseas_stock = VALUES(current_overseas_stock),
                    total_available_stock = VALUES(total_available_stock),
                    sales_7_avg = VALUES(sales_7_avg),
                    sales_14_avg = VALUES(sales_14_avg),
                    sales_30_avg = VALUES(sales_30_avg),
                    estimated_daily_sales = VALUES(estimated_daily_sales),
                    safety_min_days = VALUES(safety_min_days),
                    safety_max_days = VALUES(safety_max_days),
                    safety_normal_days = VALUES(safety_normal_days),
                    buffer_days = VALUES(buffer_days),
                    total_need_days = VALUES(total_need_days),
                    min_threshold = VALUES(min_threshold),
                    max_threshold = VALUES(max_threshold),
                    normal_threshold = VALUES(normal_threshold),
                    base_suggestion_qty = VALUES(base_suggestion_qty),
                    festival_suggestion_qty = VALUES(festival_suggestion_qty),
                    total_suggestion_qty = VALUES(total_suggestion_qty),
                    final_suggestion_qty = VALUES(final_suggestion_qty),
                    suggestion_action = VALUES(suggestion_action),
                    suggestion_reason = VALUES(suggestion_reason),
                    festival_configs = VALUES(festival_configs),
                    has_festival_impact = VALUES(has_festival_impact),
                    shipping_date = VALUES(shipping_date),
                    updated_at = CURRENT_TIMESTAMP";

        // 使用queryAll而不是execute，因为我们需要执行一个完整的SQL语句
        return $this->dbLogistics->queryAll($sql, $result);
    }

    /**
     * 清理过期的备货建议数据
     */
    public function cleanExpiredSuggestions($daysToKeep = 7)
    {
        $expireDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));

        return $this->dbLogistics->table('stock_suggestion')
            ->where('calculation_date < :expire_date', [':expire_date' => $expireDate])
            ->delete();
    }

    /**
     * 更新忽略状态
     *
     * @param string $asin
     * @param string $country
     * @param int $ignoreStatus 1=忽略, 0=取消忽略
     * @param string $operator 操作人
     * @param string $remark 备注
     * @return bool
     */
    public function updateIgnoreStatus($asin, $country, $ignoreStatus, $operator = '', $remark = '')
    {
        $updateData = [
            'ignore_status' => $ignoreStatus,
            'ignore_operator' => $operator,
            'ignore_remark' => $remark
        ];

        $result = $this->dbLogistics->table('stock_suggestion')
            ->where('asin = :asin AND country = :country AND calculation_date = :calculation_date', [
                ':asin' => $asin,
                ':country' => $country,
                ':calculation_date' => date('Y-m-d')
            ])
            ->update($updateData);

        // 如果取消忽略，需要重新生成备货建议
        if ($result && $ignoreStatus == 0) {
            $this->regenerateSuggestionForProduct($asin, $country);
        }

        return $result;
    }

    /**
     * 为指定产品重新生成备货建议
     */
    private function regenerateSuggestionForProduct($asin, $country)
    {
        $calculationDate = date('Y-m-d');

        // 生成本周建议
        $currentWeekResult = $this->calculateSingleProductSuggestion($asin, $country, 'current', $calculationDate);
        if ($currentWeekResult) {
            $this->saveSuggestionResult($currentWeekResult);
        }

        // 生成下周建议
        $nextWeekResult = $this->calculateSingleProductSuggestion($asin, $country, 'next', $calculationDate);
        if ($nextWeekResult) {
            $this->saveSuggestionResult($nextWeekResult);
        }
    }

    /**
     * 检查产品是否被忽略
     */
    public function isProductIgnored($asin, $country, $calculationDate = null)
    {
        if (!$calculationDate) {
            $calculationDate = date('Y-m-d');
        }

        $result = $this->dbLogistics->table('stock_suggestion')
            ->field('ignore_status')
            ->where('asin = :asin AND country = :country AND calculation_date = :calculation_date', [
                ':asin' => $asin,
                ':country' => $country,
                ':calculation_date' => $calculationDate
            ])
            ->one();

        return isset($result['ignore_status']) && $result['ignore_status'] == 1;
    }

    /**
     * 获取备货建议列表（支持忽略状态筛选）
     */
    public function getStockSuggestionList($filters = [], $page = 1, $pageSize = 100)
    {
        $whereConditions = ["1=1"];
        $params = [];

        // 构建筛选条件
        if (!empty($filters['country'])) {
            $whereConditions[] = "country = :country";
            $params[':country'] = $filters['country'];
        }

        if (!empty($filters['suggestion_week'])) {
            $whereConditions[] = "suggestion_week = :suggestion_week";
            $params[':suggestion_week'] = $filters['suggestion_week'];
        }

        if (!empty($filters['suggestion_action'])) {
            $whereConditions[] = "suggestion_action = :suggestion_action";
            $params[':suggestion_action'] = $filters['suggestion_action'];
        }

        if (isset($filters['ignore_status']) && $filters['ignore_status'] !== '') {
            $whereConditions[] = "ignore_status = :ignore_status";
            $params[':ignore_status'] = $filters['ignore_status'];
        }

        if (!empty($filters['asin'])) {
            $whereConditions[] = "asin LIKE :asin";
            $params[':asin'] = '%' . $filters['asin'] . '%';
        }

        if (!empty($filters['calculation_date'])) {
            $whereConditions[] = "calculation_date = :calculation_date";
            $params[':calculation_date'] = $filters['calculation_date'];
        } else {
            // 默认查询当天数据
            $whereConditions[] = "calculation_date = :calculation_date";
            $params[':calculation_date'] = date('Y-m-d');
        }

        // 构建查询条件字符串
        $whereClause = implode(' AND ', $whereConditions);

        // 使用dbLMysql的分页功能
        $result = $this->dbLogistics->table('stock_suggestion')
            ->where($whereClause, $params)
            ->order('final_suggestion_qty DESC, asin')
            ->pages($page, $pageSize);

        return [
            'total_count' => $result['total'],
            'page' => $result['page'],
            'page_size' => $pageSize,
            'total_pages' => ceil($result['total'] / $pageSize),
            'list' => $result['list']
        ];


    }
}
