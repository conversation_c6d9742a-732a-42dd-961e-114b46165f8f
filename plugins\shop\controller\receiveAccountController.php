<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\receiveAccountModel;
use plugins\shop\models\relationModel;
use Rap2hpoutre\FastExcel\FastExcel;

class receiveAccountController extends baseController
{
    // 列表
    public function getList()
    {
        $paras_list = array('account_name','account_holder', 'phone_card', 'email','customer_manager', 'user_number',
            'account_level', 'account_type', 'login_type','receive_platform', 'account_usage','relations', 'registration_date',
            'update_time','page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new receiveAccountModel();
        $list = $model->getList($param);

        $ids = array_column($list['list'], 'id');
        $relation =  (new relationModel())->getReverseRelation('receive_account', $ids);
        foreach ($list['list'] as &$item) {
            $item['relations'] = $relation[$item['id']] ?? [];
        }
        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $model = new receiveAccountModel();
        $paras_list = $model::$paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));
        $id = $_POST['id'] ?? 0;

        try {
            $model->dataValidCheck($param);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        $param = $model->jsonEncodeFormat($param);

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            if (($detail['pid'] != 0 && $param['pid'] == 0) || ($detail['pid'] == 0 && $param['pid'] != 0)) {
                returnError('账户定位不一致');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $model->add($param);
            returnSuccess([], '添加成功');
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new receiveAccountModel();
        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);
        
        if (!$detail) {
            returnError('数据不存在');
        }
        $relation =  (new relationModel())->getReverseRelation('receive_account', [$id]);
        $detail['relations'] = $relation[$item['id']] ?? [];

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id', 
        ['table_name' => 'domain', 'table_id' => $id])->order('id desc')->list();

        $model = new receiveAccountModel();
        $maps = $model->getMaps();
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);

            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 批量导入
    public function import()
    {
        $paras_list = array('excel_src');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });

        // 判断表头
        $first_user = $data[0];
        if (empty($first_user['收款账户']) || empty($first_user['注册日期']) || empty($first_user['收款平台']) ||
            empty($first_user['客户经理']) || empty($first_user['用户编号（ID）']) || empty($first_user['账户类型（公司/个人）']) ||
            empty($first_user['账户主体']) || empty($first_user['登录方式（手机号/邮箱）']) || !isset($first_user['手机号']) ||
            !isset($first_user['手机号持有人']) || !isset($first_user['手机号对接人']) || !isset($first_user['登录密码']) ||
            !isset($first_user['邮箱']) || !isset($first_user['邮箱密码']) || empty($first_user['交易密码']) ||
            !isset($first_user['安全保护问题']) || !isset($first_user['安保问题答案']) || !isset($first_user['账户定位（主账户、子账户）']) ||
            empty($first_user['是否归集']) || !isset($first_user['主账户']) || !isset($first_user['额度授权信息'])) {
            returnError('表头错误');
        }

        $phone_card = redisCached::getPhoneCard();
        $phone_manager = array_column($phone_card, 'phone_manager', 'id');
        $phone_user = array_column($phone_card, 'user_name', 'id');
        $phone_card = array_column($phone_card, 'id', 'phone_number');

        $email = redisCached::getEmail();
        $email_password = array_column($email, 'email_password', 'id');
        $email_account = array_column($email, 'id', 'email_account');

        $receive_account = redisCached::getReceiveAccount();
        $receive_account = array_column($receive_account, 'id', 'account_name');

        $model = new receiveAccountModel();
        $paras_list = $model::$paras_list;
        unset($paras_list['use_platform']);
        $import_data = [];
        $error_data = [];
        foreach ($data as $row) {
            $error_msg = [];
            empty($row['收款账户']) && $error_msg[] = '收款账户不能为空';
            try {
                $register_date = $row['注册日期']->format('Y-m-d');
                if (empty($row['注册日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '注册日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '注册日期格式错误';
            }
            empty($row['收款平台']) && $error_msg[] = '收款平台不能为空';
            empty($row['客户经理']) && $error_msg[] = '客户经理不能为空';
            empty($row['用户编号（ID）']) && $error_msg[] = '用户编号不能为空';
            empty($row['账户类型（公司/个人）']) && $error_msg[] = '账户类型不能为空';
            empty($row['账户主体']) && $error_msg[] = '账户主体不能为空';
            empty($row['登录方式（手机号/邮箱）']) && $error_msg[] = '登录方式不能为空';
            if ($row['登录方式（手机号/邮箱）'] == '手机号') {
                if (empty($row['手机号'])) {
                    $error_msg[] = '手机号不能为空';
                } else {
                    $phone_card_id = $phone_card[$row['手机号']] ?? 0;
                    if ($row['手机号持有人'] != $phone_user[$phone_card_id]) {
                        $error_msg[] = '手机号持有人不一致';
                    }
                    if ($row['手机号对接人'] != $phone_manager[$phone_card_id]) {
                        $error_msg[] = '手机号对接人不一致';
                    }
                }
            } elseif($row['登录方式（手机号/邮箱）'] == '邮箱') {
                if (empty($row['邮箱'])) {
                    $error_msg[] = '邮箱不能为空';
                } else {
                    $email_id = $email_account[$row['邮箱']] ?? 0;
                    if ($row['邮箱密码'] != $email_password[$email_id]) {
                        $error_msg[] = '邮箱密码不一致';
                    }
                }
            }

            empty($row['交易密码']) && $error_msg[] = '交易密码不能为空';
            empty($row['是否归集']) && $error_msg[] = '是否归集不能为空';
            if ($row['账户定位（主账户、子账户）'] == '子账户') {
                if (empty($row['主账户'])) {
                    $error_msg[] = '主账户不能为空';
                } else {
                    $pid = $receive_account[$row['主账户']] ?? 0;
                }
            } else {
                $pid = 0;
            }


            $item_data = [
                'account_name' => $row['收款账户'] ?? '',
                'registration_date' => $register_date ?? null,
                'receive_platform' => $row['收款平台'] ?? '',
                'customer_manager' => $row['客户经理'] ?? '',
                'user_number' => $row['用户编号'] ?? '',
                'account_type' => $row['账户类型（公司/个人）'] ?? '',
                'account_holder' => $row['账户主体'] ?? '',
                'login_type' => $row['登录方式（手机号/邮箱）'] ?? '',
                'phone_card_id' => $phone_card_id ?? 0,
                'phone_login_password' => $row['登录密码'] ?? '',
                'email_id' => $email_id ?? 0,
                'transaction_password' => $row['交易密码'] ?? '',
                'security_question' => [
                    ['q' => $row['安全保护问题'] ?? '','a' =>$row['安保问题答案'] ?? '']
                ],
                'pid' => $pid,
                'is_collected' => $row['是否归集'] ?? '',
                'credit_limit_info' => $row['额度授权信息'] ?? '',
                'remark' => $row['备注'] ?? ''
            ];

            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    // 导出
    public function export()
    {
        $paras_list = array('account_name','account_holder', 'phone_card', 'email','customer_manager', 'user_number',
            'account_level', 'account_type', 'login_type','receive_platform', 'account_usage','relations', 'registration_date',
            'update_time','ids', 'keys');
        $param = array_intersect_key($_POST, array_flip($paras_list));

        $model = new receiveAccountModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = [
                '收款账户', '注册日期', '收款平台', '使用平台', '客户经理', '用户ID', '账户类型',
                '账户主体', '登录方式', '手机号', '手机号持有人', '手机号对接人', '登录密码',
                '邮箱', '邮箱密码', '交易密码', '安全问题', '账户定位',
                '是否归集', '用途', '主账户', '额度授权信息', '备注'
            ];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '用途') {
                    $sortedItem[$key] = is_array($item['用途']) ? implode(';', $item['用途']) : ($item['用途'] ?? '');
                } elseif ($key == '账户定位') {
                    $sortedItem[$key] = (isset($item['PID']) && $item['PID'] > 0) ? '子账户' : '主账户';
                } elseif ($key == '安全问题') {
                    $question_str = '';
                    $idx = 1;
                    foreach ($item[$key] as $que) {
                        $question_str .= '【问题'.$idx.'】'.$que['q'] . ', 【答案' .$idx.'】'.$que['a'] . ';';
                        $idx++;
                    }
                    $sortedItem[$key] = $question_str;
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/receive_account_export_' . date('YmdHis') . rand(1,1000).  '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK.$filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');
    }

    // 批量编辑
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $account_name = array_column($param['data'], 'account_name');
        $account_name = array_unique($account_name);
        if (count($account_name) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的收款账户');
        }

        $model = new receiveAccountModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);

    }
}
