<?php
namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\ExceptionError;
use plugins\logistics\models\userModel;

class ReturnOrderModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }
    
    /**
     * 获取回货单列表
     * @param array $params 查询参数
     * @return array
     */
    public function getList(array $params = []): array
    {
        $where = '1=1';
        $binds = [];
        
        if (!empty($params['order_no'])) {
            $where .= ' AND order_no LIKE :order_no';
            $binds['order_no'] = '%' . $params['order_no'] . '%';
        }
        
        if (!empty($params['sku'])) {
            $where .= ' AND sku LIKE :sku';
            $binds['sku'] = '%' . $params['sku'] . '%';
        }
        
        if (!empty($params['week_period'])) {
            $where .= ' AND week_period = :week_period';
            $binds['week_period'] = $params['week_period'];
        }
        
        if (!empty($params['confirm_status'])) {
            $where .= ' AND confirm_status = :confirm_status';
            $binds['confirm_status'] = $params['confirm_status'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $offset = ($page - 1) * $pageSize;
        
        $total = $this->db->table('return_order')
            ->where($where, $binds)
            ->count();
        
        $list = $this->db->table('return_order')
            ->where($where, $binds)
            ->order('id DESC')
            ->limit($offset, $pageSize)
            ->all();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
    
    /**
     * 获取回货单详情
     * @param int $id 回货单ID
     * @return array|null
     */
    public function getDetail(int $id): ?array
    {
        $order = $this->db->table('return_order')
            ->where('id = :id', ['id' => $id])
            ->one();
        
        if (!$order) {
            return null;
        }
        
        // 获取关联的回货计划
        $plans = $this->db->table('return_plan')
            ->where('return_order_id = :order_id', ['order_id' => $id])
            ->all();
        
        $order['plans'] = $plans;
        return $order;
    }
    
    /**
     * 批量确认回货单
     * @param array $orderIds 回货单ID数组
     * @param array $confirmData 确认数据
     * @return bool
     */
    public function batchConfirm(array $orderIds, array $confirmData): bool
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($orderIds as $orderId) {
                $this->confirmOrder($orderId, $confirmData[$orderId] ?? []);
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("批量确认失败: " . $e->getMessage());
        }
    }
    
    /**
     * 确认单个回货单
     * @param int $orderId 回货单ID
     * @param array $confirmData 确认数据
     * @return bool
     */
    public function confirmOrder(int $orderId, array $confirmData = []): bool
    {
        $order = $this->db->table('return_order')
            ->where('id = :id', ['id' => $orderId])
            ->one();
        
        if (!$order) {
            throw new ExceptionError("回货单不存在");
        }
        
        if ($order['confirm_status'] === 'confirmed') {
            throw new ExceptionError("回货单已确认，无法重复确认");
        }
        
        // 保存确认时的快照数据
        $snapshotData = [
            'confirm_time' => date('Y-m-d H:i:s'),
            'total_redundant_qty' => $order['total_redundant_qty'],
            'deduct_redundant_qty' => $order['deduct_redundant_qty'],
            'actual_demand_qty' => $order['actual_demand_qty']
        ];
        
        $updateData = [
            'confirm_status' => 'confirmed',
            'confirm_qty' => $confirmData['confirm_qty'] ?? $order['actual_demand_qty'],
            'snapshot_data' => json_encode($snapshotData, JSON_UNESCAPED_UNICODE),
            'confirmed_by' => userModel::$qwuser_id,
            'confirmed_by_name' => userModel::$wname,
            'confirmed_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->db->table('return_order')
            ->where('id = :id', ['id' => $orderId])
            ->update($updateData);
    }
    
    /**
     * 重新计算回货单数据
     * @param int $orderId 回货单ID
     * @return bool
     */
    public function recalculate(int $orderId): bool
    {
        $order = $this->db->table('return_order')
            ->where('id = :id', ['id' => $orderId])
            ->one();
        
        if (!$order) {
            throw new ExceptionError("回货单不存在");
        }
        
        if ($order['confirm_status'] === 'confirmed') {
            throw new ExceptionError("已确认的回货单无法重新计算");
        }
        
        // 重新计算数据
        $calculationResult = $this->calculateOrderData($order['sku'], $order['week_period']);
        
        return $this->db->table('return_order')
            ->where('id = :id', ['id' => $orderId])
            ->update($calculationResult);
    }
    
    /**
     * 批量编辑回货单
     * @param array $updates 更新数据
     * @return bool
     */
    public function batchUpdate(array $updates): bool
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($updates as $update) {
                $orderId = $update['id'];
                unset($update['id']);
                
                $this->db->table('return_order')
                    ->where('id = :id', ['id' => $orderId])
                    ->update($update);
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("批量更新失败: " . $e->getMessage());
        }
    }
    
    /**
     * 计算回货单数据
     * @param string $sku SKU
     * @param string $weekPeriod 周期
     * @return array
     */
    private function calculateOrderData(string $sku, string $weekPeriod): array
    {
        // 获取该SKU+周期下所有已审批的计划
        $plans = $this->db->table('return_plan')
            ->where('sku = :sku AND week_period = :week_period AND status = :status', 
                ['sku' => $sku, 'week_period' => $weekPeriod, 'status' => 'approved'])
            ->all();
        
        // 计算需求合计
        $totalDemandQty = array_sum(array_column($plans, 'demand_qty'));
        
        // 获取未交冗余（从采购建议计算）
        $totalRedundantQty = $this->getRedundantQtyFromPurchaseSuggestion($sku, $weekPeriod);
        
        // 计算扣减冗余 = min(剩余冗余合计, 需求合计)
        $deductRedundantQty = min($totalRedundantQty, $totalDemandQty);
        
        // 计算实际需求量 = max((需求合计 - 扣减冗余), 0)
        $actualDemandQty = max(($totalDemandQty - $deductRedundantQty), 0);
        
        return [
            'total_demand_qty' => $totalDemandQty,
            'total_redundant_qty' => $totalRedundantQty,
            'deduct_redundant_qty' => $deductRedundantQty,
            'actual_demand_qty' => $actualDemandQty
        ];
    }
    
    /**
     * 从采购建议获取冗余数量
     * @param string $sku SKU
     * @param string $weekPeriod 周期
     * @return int
     */
    private function getRedundantQtyFromPurchaseSuggestion(string $sku, string $weekPeriod): int
    {
        // 这里需要根据实际的采购建议表结构来实现
        // 需要查询采购建议表，计算各站点的未交冗余
        
        // 示例实现（需要根据实际表结构调整）
        try {
            $redundantData = $this->db->table('purchase_suggestion')
                ->where('sku = :sku', ['sku' => $sku])
                ->all();
            
            $totalRedundant = 0;
            foreach ($redundantData as $item) {
                // 计算未交冗余的逻辑
                // 这里需要根据采购建议的具体字段来计算
                $redundant = $item['redundant_qty'] ?? 0;
                $totalRedundant += $redundant;
            }
            
            return $totalRedundant;
        } catch (\Exception $e) {
            // 如果查询失败，返回0
            return 0;
        }
    }
    
    /**
     * 获取SKU汇总数据（用于采购确认页面）
     * @param array $params 查询参数
     * @return array
     */
    public function getSkuSummary(array $params = []): array
    {
        $where = '1=1';
        $binds = [];
        
        if (!empty($params['week_period'])) {
            $where .= ' AND week_period = :week_period';
            $binds['week_period'] = $params['week_period'];
        }
        
        if (!empty($params['sku'])) {
            $where .= ' AND sku LIKE :sku';
            $binds['sku'] = '%' . $params['sku'] . '%';
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $offset = ($page - 1) * $pageSize;
        
        // 按SKU分组汇总
        $sql = "SELECT sku, 
                       COUNT(*) as order_count,
                       SUM(total_demand_qty) as total_demand,
                       SUM(actual_demand_qty) as total_actual_demand,
                       SUM(CASE WHEN confirm_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_count
                FROM return_order 
                WHERE {$where}
                GROUP BY sku
                ORDER BY sku
                LIMIT {$offset}, {$pageSize}";
        
        $list = $this->db->query($sql, $binds);
        
        // 获取总数
        $countSql = "SELECT COUNT(DISTINCT sku) as total FROM return_order WHERE {$where}";
        $totalResult = $this->db->query($countSql, $binds);
        $total = $totalResult[0]['total'] ?? 0;
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
    
    /**
     * 清空重新计算（仅未确认的）
     * @param string $weekPeriod 周期
     * @return bool
     */
    public function clearAndRecalculate(string $weekPeriod): bool
    {
        $this->db->beginTransaction();
        
        try {
            // 获取未确认的回货单
            $orders = $this->db->table('return_order')
                ->where('week_period = :week_period AND confirm_status = :status', 
                    ['week_period' => $weekPeriod, 'status' => 'pending'])
                ->all();
            
            foreach ($orders as $order) {
                $this->recalculate($order['id']);
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw new ExceptionError("清空重新计算失败: " . $e->getMessage());
        }
    }
}
