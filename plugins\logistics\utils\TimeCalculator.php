<?php

/**
 * 时间计算工具类
 * 用于备货建议模块的时间相关计算
 */
class TimeCalculator
{
    /**
     * 发货日期配置映射
     * 1=周一, 2=周二, ..., 7=周日
     */
    private static $shippingDayMap = [
        'JP' => 1, // 周一
        'DE' => 2, // 周二
        'CA' => 3, // 周三
        'IT' => 4, // 周四
        'UK' => 5, // 周五
        'FR' => 6, // 周六
        'US' => 7, // 周日
        'ES' => 1  // 周一
    ];

    /**
     * 计算到下个发货日期的缓冲天数
     * 
     * @param string $currentDate 当前日期 Y-m-d
     * @param string $weekType 'current'|'next' 本周或下周
     * @param string $country 国家代码
     * @return int 缓冲天数(不包括当天)
     */
    public static function calculateBufferDays($currentDate, $weekType, $country)
    {
        if (!isset(self::$shippingDayMap[$country])) {
            throw new InvalidArgumentException("不支持的国家代码: {$country}");
        }

        $shippingDay = self::$shippingDayMap[$country];
        $current = new DateTime($currentDate);
        $currentWeekDay = (int)$current->format('N'); // 1=周一, 7=周日

        if ($weekType === 'current') {
            // 本周发货日期
            if ($currentWeekDay <= $shippingDay) {
                // 还没到本周发货日
                $bufferDays = $shippingDay - $currentWeekDay;
            } else {
                // 已过本周发货日，计算到下周发货日
                $bufferDays = 7 - $currentWeekDay + $shippingDay;
            }
        } else { // next week
            // 下周发货日期 = 到下周一的天数 + 下周发货日的偏移
            $daysToNextWeek = 7 - $currentWeekDay; // 到下周一的天数
            $bufferDays = $daysToNextWeek + $shippingDay; // 下周一(+0) + 发货日偏移(JP=1,即周一)
        }

        return $bufferDays;
    }

    /**
     * 获取指定日期的发货日期
     * 
     * @param string $currentDate 当前日期 Y-m-d
     * @param string $weekType 'current'|'next'
     * @param string $country 国家代码
     * @return string 发货日期 Y-m-d
     */
    public static function getShippingDate($currentDate, $weekType, $country)
    {
        $bufferDays = self::calculateBufferDays($currentDate, $weekType, $country);
        $shippingDate = new DateTime($currentDate);
        $shippingDate->add(new DateInterval("P{$bufferDays}D"));
        
        return $shippingDate->format('Y-m-d');
    }

    /**
     * 判断某天是否在节日期间
     * 
     * @param string $dayOfYear 日期格式 n.j (如: 9.1)
     * @param array $festivalPeriod 节日期间 ['start' => '9.1', 'end' => '9.5']
     * @return bool
     */
    public static function isInFestivalPeriod($dayOfYear, $festivalPeriod)
    {
        $start = self::parseMonthDay($festivalPeriod['start']);
        $end = self::parseMonthDay($festivalPeriod['end']);
        $current = self::parseMonthDay($dayOfYear);

        // 处理跨年情况
        if ($start['month'] > $end['month']) {
            // 跨年节日，如12.25-1.5
            return ($current['month'] >= $start['month'] && $current['day'] >= $start['day']) ||
                   ($current['month'] <= $end['month'] && $current['day'] <= $end['day']);
        } else if ($start['month'] == $end['month']) {
            // 同月节日
            return $current['month'] == $start['month'] && 
                   $current['day'] >= $start['day'] && 
                   $current['day'] <= $end['day'];
        } else {
            // 跨月但不跨年
            return ($current['month'] == $start['month'] && $current['day'] >= $start['day']) ||
                   ($current['month'] > $start['month'] && $current['month'] < $end['month']) ||
                   ($current['month'] == $end['month'] && $current['day'] <= $end['day']);
        }
    }

    /**
     * 判断某天是否在提前备货期间
     * 
     * @param string $dayOfYear 日期格式 n.j
     * @param array $festivalPeriod 节日期间
     * @param int $dayBefore 提前天数
     * @return bool
     */
    public static function isInAdvancePeriod($dayOfYear, $festivalPeriod, $dayBefore)
    {
        $festivalStart = self::parseMonthDay($festivalPeriod['start']);
        $current = self::parseMonthDay($dayOfYear);
        
        // 计算提前期开始日期
        $advanceStartDate = DateTime::createFromFormat('n.j', $festivalPeriod['start']);
        if (!$advanceStartDate) {
            return false;
        }
        
        $advanceStartDate->sub(new DateInterval("P{$dayBefore}D"));
        $advanceStart = [
            'month' => (int)$advanceStartDate->format('n'),
            'day' => (int)$advanceStartDate->format('j')
        ];
        
        // 计算提前期结束日期（节日开始前一天）
        $advanceEndDate = DateTime::createFromFormat('n.j', $festivalPeriod['start']);
        $advanceEndDate->sub(new DateInterval('P1D'));
        $advanceEnd = [
            'month' => (int)$advanceEndDate->format('n'),
            'day' => (int)$advanceEndDate->format('j')
        ];
        
        // 判断是否在提前期间
        return self::isDateInRange($current, $advanceStart, $advanceEnd);
    }

    /**
     * 处理同一天多个节日的情况，返回最高倍数
     * 
     * @param string $dayOfYear 日期格式 n.j
     * @param array $festivalConfigs 节日配置数组
     * @param string $country 国家代码
     * @return float 最高倍数
     */
    public static function getMaxFestivalMultiplier($dayOfYear, $festivalConfigs, $country)
    {
        $maxMultiplier = 0;
        
        foreach ($festivalConfigs as $festival) {
            // 检查站点是否适用
            if (!in_array($country, $festival['sites_config'])) {
                continue;
            }
            
            $isInFestival = self::isInFestivalPeriod($dayOfYear, $festival['period']);
            $isInAdvance = self::isInAdvancePeriod($dayOfYear, $festival['period'], $festival['stock_rules']['day_before']);
            
            if ($isInFestival || $isInAdvance) {
                $multiplier = $festival['stock_rules']['prepare_value'] / 100;
                $maxMultiplier = max($maxMultiplier, $multiplier);
            }
        }
        
        return $maxMultiplier;
    }

    /**
     * 解析月日格式 n.j 为数组
     * 
     * @param string $monthDay 格式: 9.1
     * @return array ['month' => 9, 'day' => 1]
     */
    private static function parseMonthDay($monthDay)
    {
        $parts = explode('.', $monthDay);
        return [
            'month' => (int)$parts[0],
            'day' => (int)$parts[1]
        ];
    }

    /**
     * 判断日期是否在指定范围内
     * 
     * @param array $current 当前日期
     * @param array $start 开始日期
     * @param array $end 结束日期
     * @return bool
     */
    private static function isDateInRange($current, $start, $end)
    {
        // 处理跨年情况
        if ($start['month'] > $end['month']) {
            return ($current['month'] >= $start['month'] && $current['day'] >= $start['day']) ||
                   ($current['month'] <= $end['month'] && $current['day'] <= $end['day']);
        } else if ($start['month'] == $end['month']) {
            return $current['month'] == $start['month'] && 
                   $current['day'] >= $start['day'] && 
                   $current['day'] <= $end['day'];
        } else {
            return ($current['month'] == $start['month'] && $current['day'] >= $start['day']) ||
                   ($current['month'] > $start['month'] && $current['month'] < $end['month']) ||
                   ($current['month'] == $end['month'] && $current['day'] <= $end['day']);
        }
    }

    /**
     * 获取日期的月日格式
     * 
     * @param string $date Y-m-d格式日期
     * @return string n.j格式 (如: 9.1)
     */
    public static function getMonthDayFormat($date)
    {
        $dateObj = new DateTime($date);
        return $dateObj->format('n.j');
    }

    /**
     * 获取发货日期配置
     * 
     * @param string $country 国家代码
     * @return int 发货日期 (1-7)
     */
    public static function getShippingDayOfWeek($country)
    {
        return self::$shippingDayMap[$country] ?? 1;
    }
}
