<?php

namespace plugins\salary\Controller;

use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use core\lib\redisCached;
use DateTime;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\models\userModel;

class commentRewardController
{

    // 删差评列表
    public function getList()
    {
        $paras_list = ['month', 'corp_id', 'dep_id', 'user_id', 'page', 'page_size'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['month'])) returnError('月份不能为空');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;

        $sdb = dbSMysql::getInstance();
        $sdb->table('user_reward', 'a')
            ->field('a.*,c.wname as user_name, c.wdepartment_ids as user_wdepartment_ids, c.wmain_department as user_wmain_department, c.wmain_department as user_wmain_department, c.position, b.id_number,b.corp_id,b.work_place')
            ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.qwuser_id')
            ->leftJoinOut('db', 'user_info', 'b', 'b.qwuser_id=a.qwuser_id')
            ->where('where a.is_delete = 0');

        // 构建查询条件
        if (!empty($param['month'])) {
            $month = json_decode($param['month'], true);
            $sdb->andWhere('a.month >= :start_month and a.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
        }
        if (!empty($param['corp_id'])) {
            $sdb->whereIn('a.corp_id', json_decode($param['corp_id'], true));
        }

        if (!empty($param['user_id'])) {
            $sdb->whereIn('a.qwuser_id', json_decode($param['user_id'], true));
        }
        if (!empty($param['dep_id'])) {
            $qw_partment_ids = json_decode($param['dep_id'], true);
            $sdb->whereIn('c.wmain_department', $qw_partment_ids);
        }

        $sdb->order('a.create_time DESC, a.month desc, a.corp_id desc');
        $list = $sdb->pages($page, $limit);
        if (empty($list['list'])) returnSuccess($list);

        // 部门信息
        $department = redisCached::getDepartment();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = redisCached::getCorp();
        $corps = array_column($corps, null, 'id');

        // 关联用户信息
        $user_ids = array_column($list['list'], 'operator');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')
            ->whereIn('id', $user_ids)
            ->field('id, wname')
            ->list();
        $userMap = array_column($users, 'wname', 'id');

        // 处理JSON字段
        foreach ($list['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['operator_name'] = $userMap[$item['operator']] ?? '';
            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $item['user_main_department_name'] = $department[$item['user_wmain_department']] ?? '';
        }
        returnSuccess($list);

    }

    // 删差评导入
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        //删评周期,删除人员,电话号码,归属地,数量,单价金额,金额,所属公司
        $first_user = $data[0];
        if (empty($first_user['删评周期']) || empty($first_user['删除人员']) || empty($first_user['电话号码']) ||
            empty($first_user['归属地']) || empty($first_user['数量']) || empty($first_user['单价金额']) ||
            empty($first_user['金额']) || empty($first_user['所属公司'])) {
            returnError('表格格式错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 用户基本信息
        $users = $db->table('qwuser')->list();
        $users = array_column($users, null, 'wphone');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'name');

        $error_data = [];
        $res_data = [];
        foreach ($data as $item) {
            $error_msg = [];
            $dt = DateTime::createFromFormat('Y-m', $item['删评周期']);
            if (!($dt && $dt->format('Y-m') === $item['删评周期'])) {
                $error_msg[] = '删评周期格式错误';
            }
            $month = $item['删评周期'];
            if (!array_key_exists($item['电话号码'], $users)) {
                $error_msg[] = '用户不存在';
            }
            $cur_user = $users[$item['电话号码']];
//            if (empty($item['删除人员']) || $item['删除人员'] != $cur_user['wname']) {
//                $error_msg[] = '删除人员姓名错误';
//            }
            if (empty($item['所属公司']) || !array_key_exists($item['所属公司'], $corps)) {
                $error_msg[] = '所属公司不存在';
            }
            $corp_id = $corps[$item['所属公司']]['id'];

            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode('，', $error_msg)]);
                continue;
            }

            $attach = [
                'num'   => $item['数量'],
                'price' => $item['单价金额'],
                'sum'   => $item['金额'],
            ];

            $user_info_data = [
                'month'     => $month,
                'qwuser_id' => $cur_user['id'],
                'corp_id'   => $corp_id,
                'attach'    => json_encode($attach),
                'amount'    => $item['金额'],
                'operator'  => userModel::$qwuser_id
            ];
            $sdb->table('user_reward')->insert($user_info_data);
            $res_data[] = $item;
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }
    }


    // 删除删差评
    public function delete()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['id'])) {
            returnError('id不能为空');
        }
        $sdb = dbSMysql::getInstance();
        $detail = $sdb->table('user_reward')
            ->where('where id=:id', ['id' => $param['id']])
            ->one();
        if (!$detail) {
            returnError('数据不存在');
        }

        $sdb->table('user_reward')
            ->where('where id=:id', ['id' => $param['id']])
            ->update(['is_delete' => 1]);
        returnSuccess('删除成功');

    }
}