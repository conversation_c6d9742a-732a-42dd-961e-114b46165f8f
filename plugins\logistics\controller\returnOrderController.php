<?php
namespace plugins\logistics\controller;

use core\lib\ExceptionError;
use plugins\logistics\models\returnOrderModel;

class returnOrderController
{
    private $model;
    
    public function __construct()
    {
        $this->model = new returnOrderModel();
    }
    
    /**
     * 获取回货单列表
     */
    public function getList()
    {
        try {
            $params = [
                'order_no' => $_GET['order_no'] ?? '',
                'sku' => $_GET['sku'] ?? '',
                'week_period' => $_GET['week_period'] ?? '',
                'confirm_status' => $_GET['confirm_status'] ?? '',
                'page' => (int)($_GET['page'] ?? 1),
                'page_size' => (int)($_GET['page_size'] ?? 20)
            ];
            
            $result = $this->model->getList($params);
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取回货单详情
     */
    public function getDetail()
    {
        try {
            $id = (int)($_GET['id'] ?? 0);
            
            if ($id <= 0) {
                throw new ExceptionError("回货单ID不能为空");
            }
            
            $result = $this->model->getDetail($id);
            
            if (!$result) {
                throw new ExceptionError("回货单不存在");
            }
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取SKU汇总数据（采购确认页面）
     */
    public function getSkuSummary()
    {
        try {
            $params = [
                'week_period' => $_GET['week_period'] ?? '',
                'sku' => $_GET['sku'] ?? '',
                'page' => (int)($_GET['page'] ?? 1),
                'page_size' => (int)($_GET['page_size'] ?? 20)
            ];
            
            $result = $this->model->getSkuSummary($params);
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 单条确认回货单
     */
    public function confirmSingle()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['id'])) {
                throw new ExceptionError("回货单ID不能为空");
            }
            
            $confirmData = [
                'confirm_qty' => $input['confirm_qty'] ?? null
            ];
            
            $result = $this->model->confirmOrder($input['id'], $confirmData);
            
            return [
                'code' => 200,
                'message' => '确认成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 批量确认回货单（按SKU确认所有站点）
     */
    public function batchConfirmBySku()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['sku'])) {
                throw new ExceptionError("SKU不能为空");
            }
            
            if (empty($input['week_period'])) {
                throw new ExceptionError("周期不能为空");
            }
            
            // 获取该SKU+周期下的所有回货单
            $orders = $this->model->getList([
                'sku' => $input['sku'],
                'week_period' => $input['week_period'],
                'confirm_status' => 'pending',
                'page_size' => 1000
            ]);
            
            if (empty($orders['list'])) {
                throw new ExceptionError("没有找到待确认的回货单");
            }
            
            $orderIds = array_column($orders['list'], 'id');
            $confirmData = [];
            
            // 为每个回货单设置确认数据
            foreach ($orders['list'] as $order) {
                $confirmData[$order['id']] = [
                    'confirm_qty' => $input['confirm_qty'] ?? $order['actual_demand_qty']
                ];
            }
            
            $result = $this->model->batchConfirm($orderIds, $confirmData);
            
            return [
                'code' => 200,
                'message' => '批量确认成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 批量编辑回货单
     */
    public function batchEdit()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['updates']) || !is_array($input['updates'])) {
                throw new ExceptionError("更新数据不能为空");
            }
            
            // 验证更新数据
            $allowedFields = ['return_diff', 'return_rate', 'return_qty_by_thursday', 'return_rate_by_thursday'];
            $updates = [];
            
            foreach ($input['updates'] as $update) {
                if (empty($update['id'])) {
                    throw new ExceptionError("回货单ID不能为空");
                }
                
                $validUpdate = ['id' => $update['id']];
                foreach ($allowedFields as $field) {
                    if (isset($update[$field])) {
                        $validUpdate[$field] = $update[$field];
                    }
                }
                
                if (count($validUpdate) > 1) { // 除了id之外还有其他字段
                    $updates[] = $validUpdate;
                }
            }
            
            if (empty($updates)) {
                throw new ExceptionError("没有有效的更新数据");
            }
            
            $result = $this->model->batchUpdate($updates);
            
            return [
                'code' => 200,
                'message' => '批量编辑成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 重新计算回货单数据
     */
    public function recalculate()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['id'])) {
                throw new ExceptionError("回货单ID不能为空");
            }
            
            $result = $this->model->recalculate($input['id']);
            
            return [
                'code' => 200,
                'message' => '重新计算成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 清空重新计算（仅未确认的）
     */
    public function clearAndRecalculate()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['week_period'])) {
                throw new ExceptionError("周期不能为空");
            }
            
            $result = $this->model->clearAndRecalculate($input['week_period']);
            
            return [
                'code' => 200,
                'message' => '清空重新计算成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 导出回货单数据
     */
    public function export()
    {
        try {
            $params = [
                'order_no' => $_GET['order_no'] ?? '',
                'sku' => $_GET['sku'] ?? '',
                'week_period' => $_GET['week_period'] ?? '',
                'confirm_status' => $_GET['confirm_status'] ?? '',
                'page_size' => 10000 // 导出时不分页
            ];
            
            $result = $this->model->getList($params);
            
            // 设置导出头
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="return_orders_' . date('YmdHis') . '.csv"');
            header('Cache-Control: max-age=0');
            
            // 输出CSV
            $output = fopen('php://output', 'w');
            
            // 写入BOM，解决中文乱码
            fwrite($output, "\xEF\xBB\xBF");
            
            // 写入表头
            $headers = ['回货单号', 'SKU', '周期', '需求合计', '剩余冗余合计', '扣减冗余', '实际需求量', '确认状态', '采购确认数量', '确认时间'];
            fputcsv($output, $headers);
            
            // 写入数据
            foreach ($result['list'] as $row) {
                $data = [
                    $row['order_no'],
                    $row['sku'],
                    $row['week_period'],
                    $row['total_demand_qty'],
                    $row['total_redundant_qty'],
                    $row['deduct_redundant_qty'],
                    $row['actual_demand_qty'],
                    $row['confirm_status'] === 'confirmed' ? '已确认' : '待确认',
                    $row['confirm_qty'],
                    $row['confirmed_at'] ?? ''
                ];
                fputcsv($output, $data);
            }
            
            fclose($output);
            exit;
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
}
