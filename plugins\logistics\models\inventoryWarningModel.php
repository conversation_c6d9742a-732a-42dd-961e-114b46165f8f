<?php
/**
 * 库存预警模型
 * @purpose 实现库存预警的核心计算逻辑，包括6大预警指标
 * @Author: System
 * @Time: 2025/07/07
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbFMysql;
use core\lib\log;

class inventoryWarningModel
{
    private $db;
    private $erpDb;
    private $fDb;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
        $this->erpDb = dbErpMysql::getInstance();
        $this->fDb = dbFMysql::getInstance();
    }

    /**
     * 获取库存预警概览数据
     * @return array
     */
    public function getWarningOverview()
    {
        $overview = [
            'fba_inventory_warning' => $this->getFbaInventoryWarning(),
            'available_inventory_warning' => $this->getAvailableInventoryWarning(),
            'stockout_warning' => $this->getStockoutWarning(),
            'risk_store_warning' => $this->getRiskStoreWarning(),
            'store_turnover_warning' => $this->getStoreTurnoverWarning(),
            'backup_ratio_warning' => $this->getBackupRatioWarning()
        ];
        
        return $overview;
    }

    /**
     * 1. FBA库存预警
     * 统计当前存在缺货或冗余情况的产品数量（FBA库存）
     * @return array
     */
    private function getFbaInventoryWarning()
    {
        // FBA可售库存 = FBA可售 + FBA在途
        $sql = "
            SELECT 
                COUNT(CASE WHEN (fba_sellable_qty + fba_inbound_qty) < safety_stock_min THEN 1 END) as shortage_products,
                COUNT(CASE WHEN (fba_sellable_qty + fba_inbound_qty) > safety_stock_max THEN 1 END) as excess_products,
                SUM(CASE WHEN (fba_sellable_qty + fba_inbound_qty) < safety_stock_min 
                    THEN safety_stock_min - (fba_sellable_qty + fba_inbound_qty) ELSE 0 END) as shortage_quantity,
                SUM(CASE WHEN (fba_sellable_qty + fba_inbound_qty) > safety_stock_max 
                    THEN (fba_sellable_qty + fba_inbound_qty) - safety_stock_max ELSE 0 END) as excess_quantity
            FROM fba_storage_summary 
            WHERE level_type = 4 
                AND is_deleted = 0 
                AND sync_date = :sync_date
                AND (safety_stock_min > 0 OR safety_stock_max > 0)
        ";
        
        $result = $this->db->query($sql, ['sync_date' => date('Y-m-d')]);
        
        return [
            'shortage_products' => (int)($result['shortage_products'] ?? 0),
            'excess_products' => (int)($result['excess_products'] ?? 0),
            'shortage_quantity' => (int)($result['shortage_quantity'] ?? 0),
            'excess_quantity' => (int)($result['excess_quantity'] ?? 0),
            'tip' => 'FBA可售库存 = FBA可售 + FBA在途'
        ];
    }

    /**
     * 2. 可用库存预警
     * 统计当前存在缺货或冗余情况的产品数量（可用库存）
     * @return array
     */
    private function getAvailableInventoryWarning()
    {
        // 可用库存 = FBA可售 + FBA在途 + 海外仓库存 + 海外仓在途
        $sql = "
            SELECT 
                COUNT(CASE WHEN (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) < safety_stock_min THEN 1 END) as shortage_products,
                COUNT(CASE WHEN (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) > safety_stock_max THEN 1 END) as excess_products,
                SUM(CASE WHEN (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) < safety_stock_min 
                    THEN safety_stock_min - (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) ELSE 0 END) as shortage_quantity,
                SUM(CASE WHEN (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) > safety_stock_max 
                    THEN (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) - safety_stock_max ELSE 0 END) as excess_quantity
            FROM fba_storage_summary 
            WHERE level_type = 4 
                AND is_deleted = 0 
                AND sync_date = :sync_date
                AND (safety_stock_min > 0 OR safety_stock_max > 0)
        ";
        
        $result = $this->db->query($sql, ['sync_date' => date('Y-m-d')]);
        
        return [
            'shortage_products' => (int)($result['shortage_products'] ?? 0),
            'excess_products' => (int)($result['excess_products'] ?? 0),
            'shortage_quantity' => (int)($result['shortage_quantity'] ?? 0),
            'excess_quantity' => (int)($result['excess_quantity'] ?? 0),
            'tip' => '可用库存 = FBA可售 + FBA在途 + 海外仓库存 + 海外仓在途'
        ];
    }

    /**
     * 3. 断货预警
     * 统计当前存在断货的产品数量（FBA库存）及平均断货天数
     * @return array
     */
    private function getStockoutWarning()
    {
        // 当前断货产品（FBA库存为0且日均销量>0）
        $currentStockoutSql = "
            SELECT 
                COUNT(*) as stockout_products,
                AVG(CASE WHEN daily_avg_sales_qty > 0 THEN 
                    DATEDIFF(CURDATE(), DATE_SUB(CURDATE(), INTERVAL (fba_sellable_qty + fba_inbound_qty) / daily_avg_sales_qty DAY))
                    ELSE 0 END) as avg_stockout_days
            FROM fba_storage_summary 
            WHERE level_type = 4 
                AND is_deleted = 0 
                AND sync_date = :sync_date
                AND (fba_sellable_qty + fba_inbound_qty) = 0
                AND daily_avg_sales_qty > 0
        ";
        
        // 未来20天内可能断货的产品
        $futureStockoutSql = "
            SELECT 
                COUNT(*) as future_stockout_products,
                AVG(CASE WHEN daily_avg_sales_qty > 0 THEN 
                    (fba_sellable_qty + fba_inbound_qty) / daily_avg_sales_qty
                    ELSE 0 END) as avg_sellable_days
            FROM fba_storage_summary 
            WHERE level_type = 4 
                AND is_deleted = 0 
                AND sync_date = :sync_date
                AND daily_avg_sales_qty > 0
                AND (fba_sellable_qty + fba_inbound_qty) / daily_avg_sales_qty < 20
                AND (fba_sellable_qty + fba_inbound_qty) > 0
        ";
        
        $currentResult = $this->db->query($currentStockoutSql, ['sync_date' => date('Y-m-d')]);
        $futureResult = $this->db->query($futureStockoutSql, ['sync_date' => date('Y-m-d')]);
        
        return [
            'current_stockout_products' => (int)($currentResult['stockout_products'] ?? 0),
            'avg_stockout_days' => round($currentResult['avg_stockout_days'] ?? 0, 1),
            'future_stockout_products' => (int)($futureResult['future_stockout_products'] ?? 0),
            'avg_sellable_days' => round($futureResult['avg_sellable_days'] ?? 0, 1),
            'tip' => '即将断货指预计在未来20天内可能断货的产品'
        ];
    }

    /**
     * 4. 风险店铺库存预警
     * 统计库存超出上限的风险店铺数量，汇总这些店铺的超限库存总量
     * @return array
     */
    private function getRiskStoreWarning()
    {
        // 这里需要根据实际的风险店铺配置表来实现
        // 暂时返回模拟数据，后续需要根据实际配置表调整
        return [
            'risk_stores_count' => 0,
            'excess_inventory_total' => 0,
            'tip' => '仅统计库存超出上限的风险店铺数量'
        ];
    }

    /**
     * 5. 店铺流水预警
     * 统计流水超出限制的店铺数量和对应超限金额
     * @return array
     */
    private function getStoreTurnoverWarning()
    {
        // 这里需要根据实际的流水预警规则来实现
        // 暂时返回模拟数据，后续需要根据实际规则调整
        return [
            'main_backup_stores' => 0,
            'main_backup_excess_amount' => 0,
            'secondary_backup_stores' => 0,
            'secondary_backup_excess_amount' => 0,
            'fk_review_stores' => 0,
            'fk_review_excess_amount' => 0,
            'brush_stores' => 0,
            'brush_excess_amount' => 0,
            'tip' => '14天流水预警\n主备货号 > $50,000\n次备货号 > $30,000\nFK回评号 > $20,000\n刷单号 > $20,000'
        ];
    }

    /**
     * 6. 备货号库存占比
     * 统计主次备货号占比偏离正常范围的产品
     * @return array
     */
    private function getBackupRatioWarning()
    {
        // 这里需要根据实际的备货规则配置来实现
        // 暂时返回模拟数据，后续需要根据实际规则调整
        return [
            'abnormal_ratio_products' => 0,
            'tip' => '主次备货号占比偏离正常范围的产品\n新品期、成长期：15%~25%\n稳定期、衰退期：55%~65%'
        ];
    }

    /**
     * 获取库存明细汇总列表
     * @param array $params 查询参数
     * @return array
     */
    public function getInventoryDetailList($params = [])
    {
        $query = $this->buildDetailQuery($params);

        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;

        $result = $query->order('id DESC')
            ->pages($page, $pageSize);

        if (!empty($result['list'])) {
            $result['list'] = $this->formatDetailList($result['list'], $params);
        }

        return $result;
    }

    /**
     * 构建明细查询
     * @param array $params
     * @return object
     */
    private function buildDetailQuery($params)
    {
        $query = $this->db->table('fba_storage_summary')
            ->where('is_deleted = 0 AND sync_date = :sync_date', ['sync_date' => date('Y-m-d')]);

        // 根据显示维度确定level_type
        $showGroupAndSite = isset($params['show_group']) && $params['show_group'] == 1
                         && isset($params['show_site']) && $params['show_site'] == 1;

        if ($showGroupAndSite) {
            // 显示组别和站点时，按店铺级显示
            $query->andWhere('level_type = 1');
        } else {
            // 不显示时，按SKU汇总显示
            $query->andWhere('level_type = 3');
        }

        // ASIN筛选
        if (!empty($params['asin'])) {
            $query->andWhere('asin LIKE :asin', ['asin' => '%' . $params['asin'] . '%']);
        }

        // SKU筛选
        if (!empty($params['sku'])) {
            $query->andWhere('sku LIKE :sku', ['sku' => '%' . $params['sku'] . '%']);
        }

        // 品名筛选
        if (!empty($params['product_name'])) {
            $query->andWhere('product_name LIKE :product_name', ['product_name' => '%' . $params['product_name'] . '%']);
        }

        // 站点筛选
        if (!empty($params['site_code'])) {
            $query->andWhere('site_code = :site_code', ['site_code' => $params['site_code']]);
        }

        // 产品分类筛选
        if (!empty($params['category_name'])) {
            $query->andWhere('category_name LIKE :category_name', ['category_name' => '%' . $params['category_name'] . '%']);
        }

        // Listing阶段筛选
        if (!empty($params['product_stage'])) {
            $query->andWhere('product_stage = :product_stage', ['product_stage' => $params['product_stage']]);
        }

        // 运营组别筛选
        if (!empty($params['operation_group'])) {
            $query->andWhere('operation_group LIKE :operation_group', ['operation_group' => '%' . $params['operation_group'] . '%']);
        }

        // 只显示有预警的产品
        $query->andWhere('(
            (fba_sellable_qty + fba_inbound_qty) < safety_stock_min OR
            (fba_sellable_qty + fba_inbound_qty) > safety_stock_max OR
            (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) < safety_stock_min OR
            (fba_sellable_qty + fba_inbound_qty + overseas_warehouse_qty + overseas_warehouse_inbound_qty) > safety_stock_max
        )');

        return $query;
    }

    /**
     * 格式化明细列表数据
     * @param array $list
     * @param array $params
     * @return array
     */
    private function formatDetailList($list, $params)
    {
        foreach ($list as &$item) {
            // 计算FBA可售库存
            $fbaInventory = $item['fba_sellable_qty'] + $item['fba_inbound_qty'];

            // 计算可用库存
            $availableInventory = $fbaInventory + $item['overseas_warehouse_qty'] + $item['overseas_warehouse_inbound_qty'];

            // 计算预警状态和数量
            $item['fba_warning'] = $this->calculateWarning($fbaInventory, $item['safety_stock_min'], $item['safety_stock_max']);
            $item['available_warning'] = $this->calculateWarning($availableInventory, $item['safety_stock_min'], $item['safety_stock_max']);

            // 计算预计可售天数（排除新品期15天内的产品）
            $item['fba_sellable_days'] = $this->calculateSellableDays($fbaInventory, $item['daily_avg_sales_qty'], $item);
            $item['available_sellable_days'] = $this->calculateSellableDays($availableInventory, $item['daily_avg_sales_qty'], $item);

            // 设置产品标识
            $item['product_tags'] = $this->getProductTags($item);

            // 格式化显示字段
            $item['product_stage_text'] = $this->getProductStageText($item['product_stage']);
            $item['stock_positioning_text'] = $this->getStockPositioningText($item['stock_positioning']);

            // 四舍五入处理
            $item['daily_avg_sales_qty'] = round($item['daily_avg_sales_qty'], 0);
            $item['fba_sellable_days'] = round($item['fba_sellable_days'], 0);
            $item['available_sellable_days'] = round($item['available_sellable_days'], 0);
        }

        return $list;
    }

    /**
     * 计算预警状态和数量
     * @param int $inventory 当前库存
     * @param int $minStock 安全库存下限
     * @param int $maxStock 安全库存上限
     * @return array
     */
    private function calculateWarning($inventory, $minStock, $maxStock)
    {
        if ($minStock > 0 && $inventory < $minStock) {
            return [
                'type' => 'shortage',
                'quantity' => $minStock - $inventory,
                'text' => '缺货' . ($minStock - $inventory)
            ];
        } elseif ($maxStock > 0 && $inventory > $maxStock) {
            return [
                'type' => 'excess',
                'quantity' => $inventory - $maxStock,
                'text' => '冗余' . ($inventory - $maxStock)
            ];
        }

        return [
            'type' => 'normal',
            'quantity' => 0,
            'text' => ''
        ];
    }

    /**
     * 计算预计可售天数
     * @param int $inventory 库存数量
     * @param float $dailySales 日均销量
     * @param array $item 产品信息
     * @return float
     */
    private function calculateSellableDays($inventory, $dailySales, $item)
    {
        // 新品期产品首单时间低于15个自然日时，不计入天数核算
        if ($this->isNewProduct($item)) {
            return 0;
        }

        if ($dailySales > 0) {
            return $inventory / $dailySales;
        }

        return 0;
    }

    /**
     * 判断是否为新品期产品（首单时间低于15天）
     * @param array $item
     * @return bool
     */
    private function isNewProduct($item)
    {
        // 这里需要根据实际的首单时间字段来判断
        // 暂时根据产品阶段判断
        return $item['product_stage'] == 4; // 4表示新品期
    }

    /**
     * 获取产品标识
     * @param array $item
     * @return array
     */
    private function getProductTags($item)
    {
        $tags = [];

        // 需发货标识
        if ($item['planned_purchase_qty'] > 0) {
            $tags[] = '发';
        }

        // 需采购标识
        if ($item['purchase_pending_qty'] > 0) {
            $tags[] = '采';
        }

        // 库存冗余标识
        $fbaInventory = $item['fba_sellable_qty'] + $item['fba_inbound_qty'];
        $availableInventory = $fbaInventory + $item['overseas_warehouse_qty'] + $item['overseas_warehouse_inbound_qty'];

        if (($item['safety_stock_max'] > 0 && $fbaInventory > $item['safety_stock_max']) ||
            ($item['safety_stock_max'] > 0 && $availableInventory > $item['safety_stock_max'])) {
            $tags[] = '冗';
        }

        return $tags;
    }

    /**
     * 获取产品阶段文本
     * @param int $stage
     * @return string
     */
    private function getProductStageText($stage)
    {
        $map = [
            1 => '成长期',
            2 => '稳定期',
            3 => '衰退期',
            4 => '新品期',
            5 => '清货'
        ];

        return $map[$stage] ?? '';
    }

    /**
     * 获取备货定位文本
     * @param int $positioning
     * @return string
     */
    private function getStockPositioningText($positioning)
    {
        $map = [
            1 => '重点备货',
            2 => '常规备货',
            3 => '停止备货'
        ];

        return $map[$positioning] ?? '';
    }
}
