<?php

namespace plugins\logistics\controller;

use plugins\logistics\models\StockSuggestionModel;

class stockSuggestionController
{
    /**
     * 获取备货建议列表
     * @return void
     */
    public function getList()
    {
        $country = $_POST['country'] ?? '';
        $weekType = $_POST['week_type'] ?? 'current'; // current/next
        $suggestionAction = $_POST['suggestion_action'] ?? ''; // suggest/redundant/normal
        $page = (int)($_POST['page'] ?? 1);
        $pageSize = (int)($_POST['page_size'] ?? 100);
        $calculationDate = $_POST['calculation_date'] ?? date('Y-m-d');

        $stockSuggestionModel = new StockSuggestionModel();
        $db = $stockSuggestionModel->dbLogistics;
        $db->table('stock_suggestion')->where('calculation_date = :calculation_date', [':calculation_date' => $calculationDate]);


        if (!empty($country)) {
            $db->andWhere('country = :country', [':country' => $country]);
        }

        if (!empty($weekType)) {
            $db->andWhere('suggestion_week = :week_type', [':week_type' => $weekType]);
        }

        if (!empty($suggestionAction)) {
            $db->andWhere('suggestion_action = :suggestion_action', [':suggestion_action' => $suggestionAction]);
        }

        $list = $db->pages($page, $pageSize);

        returnSuccess($list);
    }

    public function setIgnoreStatus()
    {
        $id = $_POST['id'] ?? '';
        $ignoreStatus = (int)($_POST['ignore_status'] ?? 0);

        $stockSuggestionModel = new StockSuggestionModel();
        $result = $stockSuggestionModel->updateIgnoreStatus($id, $ignoreStatus);

        returnSuccess([], '设置成功');
    }
}
