<?php

/**
 * FBA冗余标记模型
 * 用于自动标记和管理FBA库存冗余状态
 */
class FbaRedundantModel
{
    private $dbErp;
    private $dbLogistics;

    public function __construct()
    {
        $this->dbErp = dbErpMysql::getInstance();
        $this->dbLogistics = dbLMysql::getInstance();
    }

    /**
     * 更新FBA冗余状态
     * 根据备货建议结果更新fba_storage_summary表的冗余标记
     * 
     * @param string $calculationDate 计算日期
     * @return array 更新结果统计
     */
    public function updateFbaRedundantStatus($calculationDate = null)
    {
        if (!$calculationDate) {
            $calculationDate = date('Y-m-d');
        }

        try {
            $this->dbErp->beginTransaction();

            // 1. 清除所有冗余标记
            $this->clearAllRedundantMarks();

            // 2. 根据备货建议标记冗余库存
            $redundantCount = $this->markRedundantStock($calculationDate);

            // 3. 更新建议状态
            $suggestedCount = $this->markSuggestedStock($calculationDate);

            $this->dbErp->commit();

            return [
                'success' => true,
                'calculation_date' => $calculationDate,
                'redundant_count' => $redundantCount,
                'suggested_count' => $suggestedCount,
                'updated_at' => date('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            $this->dbErp->rollBack();
            throw $e;
        }
    }

    /**
     * 清除所有冗余标记
     */
    private function clearAllRedundantMarks()
    {
        $sql = "UPDATE fba_storage_summary 
                SET is_redundant = 0, 
                    suggestion_status = 'normal',
                    redundant_updated_at = NULL";
        
        return $this->dbErp->execute($sql);
    }

    /**
     * 标记冗余库存
     */
    private function markRedundantStock($calculationDate)
    {
        $sql = "UPDATE fba_storage_summary f
                INNER JOIN (
                    SELECT DISTINCT asin, country
                    FROM stock_suggestion 
                    WHERE calculation_date = :calculation_date 
                    AND suggestion_action = 'redundant'
                ) s ON f.asin = s.asin AND f.country = s.country
                SET f.is_redundant = 1,
                    f.suggestion_status = 'redundant',
                    f.redundant_updated_at = CURRENT_TIMESTAMP";

        $result = $this->dbErp->execute($sql, [':calculation_date' => $calculationDate]);
        
        return $this->dbErp->rowCount();
    }

    /**
     * 标记需要备货的库存
     */
    private function markSuggestedStock($calculationDate)
    {
        $sql = "UPDATE fba_storage_summary f
                INNER JOIN (
                    SELECT DISTINCT asin, country
                    FROM stock_suggestion 
                    WHERE calculation_date = :calculation_date 
                    AND suggestion_action = 'suggest'
                ) s ON f.asin = s.asin AND f.country = s.country
                SET f.suggestion_status = 'suggest',
                    f.redundant_updated_at = CURRENT_TIMESTAMP";

        $result = $this->dbErp->execute($sql, [':calculation_date' => $calculationDate]);
        
        return $this->dbErp->rowCount();
    }

    /**
     * 获取冗余库存统计
     * 
     * @param string $country 国家筛选，可选
     * @return array
     */
    public function getRedundantStockStatistics($country = null)
    {
        $whereClause = $country ? "WHERE country = :country" : "";
        $params = $country ? [':country' => $country] : [];

        $sql = "SELECT 
                    country,
                    COUNT(*) as total_products,
                    SUM(CASE WHEN is_redundant = 1 THEN 1 ELSE 0 END) as redundant_count,
                    SUM(CASE WHEN suggestion_status = 'suggest' THEN 1 ELSE 0 END) as suggest_count,
                    SUM(CASE WHEN suggestion_status = 'normal' THEN 1 ELSE 0 END) as normal_count,
                    SUM(CASE WHEN is_redundant = 1 THEN fba_sellable_qty ELSE 0 END) as redundant_qty,
                    SUM(CASE WHEN suggestion_status = 'suggest' THEN fba_sellable_qty ELSE 0 END) as suggest_qty
                FROM fba_storage_summary 
                {$whereClause}
                GROUP BY country
                ORDER BY country";

        return $this->dbErp->fetchAll($sql, $params);
    }

    /**
     * 获取冗余库存明细
     * 
     * @param array $filters 筛选条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getRedundantStockDetails($filters = [], $page = 1, $pageSize = 100)
    {
        $whereConditions = ["1=1"];
        $params = [];

        // 构建筛选条件
        if (!empty($filters['country'])) {
            $whereConditions[] = "country = :country";
            $params[':country'] = $filters['country'];
        }

        if (!empty($filters['suggestion_status'])) {
            $whereConditions[] = "suggestion_status = :suggestion_status";
            $params[':suggestion_status'] = $filters['suggestion_status'];
        }

        if (!empty($filters['is_redundant'])) {
            $whereConditions[] = "is_redundant = :is_redundant";
            $params[':is_redundant'] = $filters['is_redundant'];
        }

        if (!empty($filters['asin'])) {
            $whereConditions[] = "asin LIKE :asin";
            $params[':asin'] = '%' . $filters['asin'] . '%';
        }

        $whereClause = implode(' AND ', $whereConditions);

        // 计算总数
        $countSql = "SELECT COUNT(*) FROM fba_storage_summary WHERE {$whereClause}";
        $totalCount = $this->dbErp->fetchOne($countSql, $params);

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $sql = "SELECT 
                    asin, sku, country, product_stage,
                    fba_sellable_qty, overseas_warehouse_qty,
                    is_redundant, suggestion_status, redundant_updated_at,
                    created_at, updated_at
                FROM fba_storage_summary 
                WHERE {$whereClause}
                ORDER BY redundant_updated_at DESC, asin
                LIMIT {$offset}, {$pageSize}";

        $details = $this->dbErp->fetchAll($sql, $params);

        return [
            'total_count' => $totalCount,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => ceil($totalCount / $pageSize),
            'data' => $details
        ];
    }

    /**
     * 手动设置冗余状态
     * 
     * @param string $asin
     * @param string $country
     * @param int $isRedundant 1=冗余, 0=正常
     * @param string $reason 原因说明
     * @return bool
     */
    public function setRedundantStatus($asin, $country, $isRedundant, $reason = '')
    {
        $suggestionStatus = $isRedundant ? 'redundant' : 'normal';
        
        $sql = "UPDATE fba_storage_summary 
                SET is_redundant = :is_redundant,
                    suggestion_status = :suggestion_status,
                    redundant_updated_at = CURRENT_TIMESTAMP
                WHERE asin = :asin AND country = :country";

        $params = [
            ':is_redundant' => $isRedundant,
            ':suggestion_status' => $suggestionStatus,
            ':asin' => $asin,
            ':country' => $country
        ];

        $result = $this->dbErp->execute($sql, $params);

        // 记录操作日志
        if ($result && !empty($reason)) {
            $this->logRedundantOperation($asin, $country, $isRedundant, $reason);
        }

        return $result;
    }

    /**
     * 记录冗余操作日志
     */
    private function logRedundantOperation($asin, $country, $isRedundant, $reason)
    {
        $action = $isRedundant ? 'mark_redundant' : 'clear_redundant';
        
        $logSql = "INSERT INTO operation_log (
                       module, action, target_id, target_type, 
                       description, operator, created_at
                   ) VALUES (
                       'stock_suggestion', :action, :target_id, 'fba_stock',
                       :description, 'system', CURRENT_TIMESTAMP
                   )";

        $logParams = [
            ':action' => $action,
            ':target_id' => $asin . '_' . $country,
            ':description' => "ASIN: {$asin}, Country: {$country}, Reason: {$reason}"
        ];

        try {
            $this->dbLogistics->execute($logSql, $logParams);
        } catch (Exception $e) {
            // 日志记录失败不影响主要操作
            error_log("Failed to log redundant operation: " . $e->getMessage());
        }
    }

    /**
     * 获取冗余趋势数据
     * 
     * @param int $days 天数
     * @return array
     */
    public function getRedundantTrend($days = 30)
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    DATE(redundant_updated_at) as date,
                    COUNT(*) as redundant_count,
                    SUM(fba_sellable_qty) as redundant_qty
                FROM fba_storage_summary 
                WHERE is_redundant = 1 
                AND redundant_updated_at >= :start_date
                GROUP BY DATE(redundant_updated_at)
                ORDER BY date";

        return $this->dbErp->fetchAll($sql, [':start_date' => $startDate]);
    }
}
