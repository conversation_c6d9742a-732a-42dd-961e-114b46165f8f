#!/bin/bash

# 测试备货建议重构脚本
# 简化版本，用于验证API调用逻辑

API_URL="http://oa.ywx.com/task/logistics"
TOKEN="01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0"

echo "开始测试备货建议批量处理..."

# 测试API调用
response=$(curl -s -X POST "$API_URL/batchStockSuggestionUpdate" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "token=$TOKEN&calculation_date=$(date +%Y-%m-%d)&week_type=current" \
    --max-time 30)

echo "API响应: $response"

# 提取code字段
code=$(echo "$response" | grep -oP '"code":\K-?\d+')
echo "返回码: $code"

if [ "$code" -eq 2 ]; then
    echo "✅ 测试成功 - 处理完成"
elif [ "$code" -eq 1 ]; then
    echo "🔄 测试成功 - 需要继续处理"
else
    echo "❌ 测试失败 - 错误码: $code"
fi

echo "测试完成"
