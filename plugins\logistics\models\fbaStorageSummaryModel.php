<?php
/**
 * FBA库存汇总统计模型
 * @purpose FBA库存分层汇总统计数据处理
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;
use core\lib\log;

class fbaStorageSummaryModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 获取汇总统计列表
     * @param array $params 查询参数
     * @return array
     */
    public function getSummaryList($params = [])
    {
        $query = $this->db->table('fba_storage_summary')->where('level_type = 4'); // 主数据只查询asin级
        
        // ASIN筛选
        if (!empty($params['asin'])) {
            $query->andWhere('asin LIKE :asin', ['asin' => '%' . $params['asin'] . '%']);
        }
        
        // SKU筛选
        if (!empty($params['sku'])) {
            $query->andWhere('sku LIKE :sku', ['sku' => '%' . $params['sku'] . '%']);
        }

        // 品名
        if (!empty($params['product_name'])) {
            $query->andWhere('product_name LIKE :product_name', ['product_name' => '%' . $params['product_name'] . '%']);
        }

        // 店铺
        if (!empty($params['shop_name'])) {
            $query->andWhere('shop_name LIKE :shop_name', ['shop_name' => '%' . $params['shop_name'] . '%']);
        }
        
        // 站点筛选
        if (!empty($params['country_code'])) {
            $query->andWhere('country_code = :country_code', ['country_code' => $params['country_code']]);
        }

        // 类目
        if (!empty($params['category_name'])) {
            $query->andWhere('category_name LIKE :category_name', ['category_name' => '%' . $params['category_name'] . '%']);
        }
        
        // listing阶段筛选
        if (!empty($params['product_stage'])) {
            $query->andWhere('product_stage = :product_stage', ['product_stage' => $params['product_stage']]);
        }
        
        // 备货定位筛选
        if (!empty($params['stock_positioning'])) {
            $query->andWhere('stock_positioning = :stock_positioning', ['stock_positioning' => $params['stock_positioning']]);
        }
        
        // 产品定位筛选
        if (!empty($params['product_positioning'])) {
            $query->andWhere('product_positioning = :product_positioning', ['product_positioning' => $params['product_positioning']]);
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        
        return $query->order('id asc')
            ->pages($page, $pageSize);
    }

    /**
     * 获取子项
     * @param [type] $asin
     * @return array
     */
    public function getChildren($asin)
    {
        $list = $this->db->table('fba_storage_summary')
        ->where('level_type != 4')
        ->whereIn('asin',$asin)
        ->order('asin asc, level_type desc')
        ->list();

        foreach ($list as &$item) {
            $item['level_type_text'] = $levelTypeMap[$item['level_type']] ?? '';
                    $item['product_stage_text'] = $productStageMap[$item['product_stage']] ?? '';
                    $item['stock_positioning_text'] = $stockPositioningMap[$item['stock_positioning']] ?? '';
                    $item['product_positioning_text'] = $productPositioningMap[$item['product_positioning']] ?? '';

            // 解析JSON字段
            $list_key = ['shop_list', 'country_code_list', 'sku_list', 'product_name_list',
                'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list', 'operation_user_ids', 'stock_user_ids'];
            foreach ($list_key as $key) {
                if (!empty($item[$key])) {
                    $item[$key] = json_decode($item[$key], true) ?: [];
                }
            }
        }

        return $list;
        
    }

    /**
     * 更新可编辑字段
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updateEditableFields($id, $data)
    {
        $allowedFields = [
            'planned_purchase_qty',
            'purchase_pending_qty'
        ];
        
        $updateData = [];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            return 0;
        }
                
        return $this->db->table('fba_storage_summary')
            ->where('id = :id ', ['id' => $id])
            ->update($updateData);
    }


    /**
     * 初始化维度数据结构
     * @param int $levelType
     * @param array $row
     * @param string $syncDate
     * @return array
     */
    private function initDimensionData($levelType, $row, $syncDate)
    {
        $data = [
            'level_type' => $levelType,
            'asin' => $row['asin'],
            'sku' => '',
            'country_code' => '',
            'sid' => 0,
            'shop_name' => '',
            'product_name' => '',
            'small_image_url' => '',
            'product_stage' => $row['product_stage'],
            'stock_positioning' => $row['stock_positioning'],
            'product_positioning' => $row['product_positioning'],
            'fba_sellable_qty' => 0,
            'fba_pending_transfer_qty' => 0,
            'fba_transferring_qty' => 0,
            'fba_inbound_qty' => 0,
            'inventory_plus_inbound_qty' => 0,
            'fba_sellable_price' => 0.00,
            'fba_pending_transfer_price' => 0.00,
            'fba_transferring_price' => 0.00,
            'fba_inbound_price' => 0.00,
            'inventory_plus_inbound_price' => 0.00,
            'operation_user_ids' => [],
            'stock_user_ids' => [],
            'shop_detail' => [],
            'shop_list' => [],
            'country_code_list' => [],
            'sku_list' => [],
            'product_name_list' => [],
            'warehouse_list' => [],
            'seller_sku_list' => [],
            'fnsku_list' => [],
            'category_list' => [],
            'brand_list' => [],
            'sync_date' => $syncDate,
        ];
        
        // 根据维度设置相应字段
        switch ($levelType) {
            case 1: // 店铺级
                $data['sku'] = $row['sku'];
                $data['country_code'] = $row['country_code'];
                $data['sid'] = $row['sid'];
                $data['product_name'] = $row['product_name'];
                $data['small_image_url'] = $row['small_image_url'];
                break;
            case 2: // 站点级
                $data['sku'] = $row['sku'];
                $data['country_code'] = $row['country_code'];
                $data['product_name'] = $row['product_name'];
                $data['small_image_url'] = $row['small_image_url'];
                break;
            case 3: // SKU级
                $data['sku'] = $row['sku'];
                $data['product_name'] = $row['product_name'];
                $data['small_image_url'] = $row['small_image_url'];
                break;
            case 4: // ASIN级
                break;
        }
        
        return $data;
    }


    /**
     * 获取层级类型映射
     * @return array
     */
    public function getLevelTypeMap()
    {
        return [
            1 => '店铺级',
            2 => '站点级', 
            3 => 'SKU级',
            4 => 'ASIN级'
        ];
    }

    /**
     * 获取产品阶段映射
     * @return array
     */
    public function getProductStageMap()
    {
        return [
            1 => '成长期',
            2 => '稳定期',
            3 => '衰退期',
            4 => '新品期',
            5 => '清货'
        ];
    }

    /**
     * 获取备货定位映射
     * @return array
     */
    public function getStockPositioningMap()
    {
        return [
            1 => '重点备货',
            2 => '常规备货',
            3 => '停止备货'
        ];
    }

    /**
     * 获取产品定位映射
     * @return array
     */
    public function getProductPositioningMap()
    {
        return [
            1 => '头部产品',
            2 => '腰部产品',
            3 => '尾部产品',
            4 => '清货产品'
        ];
    }


    /**
     * 获取源数据总数
     * @param string $syncDate 同步日期
     * @return int
     */
    public function getSourceDataCount($syncDate)
    {
        $sourceDb = dbErpMysql::getInstance();
        $result = $sourceDb->table('lingxing_fba_storage_detail')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->count();
        return $result ?: 0;
    }

    /**
     * 清理目标表中当天的数据
     * @param string $syncDate 同步日期
     * @return void
     */
    public function clearTargetData($syncDate)
    {
        $this->db->table('fba_storage_summary')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->delete();
    }

    /**
     * 分批获取源数据（不包含关联数据，只获取基础数据）
     * @param string $syncDate 同步日期
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array
     */
    public function getSourceDataBatch($syncDate, $page, $page_size)
    {
        $sourceDb = dbErpMysql::getInstance();
        return $sourceDb->table('lingxing_fba_storage_detail')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->pages($page, $page_size);
    }

    /**
     * 处理批量数据的四维度汇总
     * @param array $batchData 批量数据
     * @param string $syncDate 同步日期
     * @return array 四维度汇总数据
     */
    public function processBatchSummary($batchData, $syncDate)
    {
        $dimensions = [
            1 => [], // 店铺级：asin+sku+country_code+sid
            2 => [], // 站点级：asin+sku+country_code
            3 => [], // SKU级： asin+sku
            4 => []  // ASIN级：asin
        ];

        foreach ($batchData as $row) {
            // 计算各种数量和价格
            $quantities = $this->calculateQuantities($row);
            $prices = $this->calculatePrices($row);

            // 维度1：店铺级 (asin+sku+country_code+sid)
            $key1 = $row['asin'] . '|' . $row['sku'] . '|' . $row['country_code'] . '|' . $row['sid'];
            if (!isset($dimensions[1][$key1])) {
                $dimensions[1][$key1] = $this->initDimensionData(1, $row, $syncDate);
            }
            $this->aggregateData($dimensions[1][$key1], $quantities, $prices, $row);

            // 维度2：站点级 (asin+sku+country_code)
            $key2 = $row['asin'] . '|' . $row['sku'] . '|' . $row['country_code'];
            if (!isset($dimensions[2][$key2])) {
                $dimensions[2][$key2] = $this->initDimensionData(2, $row, $syncDate);
            }
            $this->aggregateData($dimensions[2][$key2], $quantities, $prices, $row);

            // 维度3：SKU级 (asin+sku)
            $key3 = $row['asin'] . '|' . $row['sku'];
            if (!isset($dimensions[3][$key3])) {
                $dimensions[3][$key3] = $this->initDimensionData(3, $row, $syncDate);
            }
            $this->aggregateData($dimensions[3][$key3], $quantities, $prices, $row);

            // 维度4：ASIN级 (asin)
            $key4 = $row['asin'];
            if (!isset($dimensions[4][$key4])) {
                $dimensions[4][$key4] = $this->initDimensionData(4, $row, $syncDate);
            }
            $this->aggregateData($dimensions[4][$key4], $quantities, $prices, $row);
        }

        // 合并所有维度数据
        $result = [];
        foreach ($dimensions as $levelType => $levelData) {
            foreach ($levelData as $data) {
                $result[] = $data;
            }
        }

        return $result;
    }

    /**
     * 批量插入汇总数据（新版本，接收数组格式的汇总数据）
     * @param array $summaryData 汇总数据数组
     * @return array 插入结果统计
     */
    public function batchInsertSummaryData($summaryData)
    {
        $result = ['inserted' => 0, 'updated' => 0];

        foreach ($summaryData as $data) {
            // 转换数组字段为JSON
            $list_key = ['shop_list', 'country_code_list', 'sku_list', 'product_name_list',
                'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list', 'operation_user_ids', 'stock_user_ids'];
            foreach ($list_key as $key) {
                if (is_array($data[$key])) {
                    $data[$key] = json_encode($data[$key], JSON_UNESCAPED_UNICODE);
                }
            }

            try {
                // 检查是否已存在相同维度的记录
                $existingId = $this->checkExistingRecord($this->db, $data);

                if ($existingId) {
                    // 更新现有记录
                    $this->updateExistingRecord($this->db, $existingId, $data);
                    $result['updated']++;
                } else {
                    // 插入新记录
                    $this->db->table('fba_storage_summary')->insert($data);
                    $result['inserted']++;
                }
            } catch (\Exception $e) {
                throw $e;
            }
        }

        return $result;
    }

    /**
     * 计算数量相关字段
     * @param array $row 数据行
     * @return array
     */
    private function calculateQuantities($row)
    {
        return [
            'fba_sellable_qty' => (int)($row['afn_fulfillable_quantity'] ?? 0),
            'fba_pending_transfer_qty' => (int)($row['reserved_fc_transfers'] ?? 0),
            'fba_transferring_qty' => (int)($row['reserved_fc_processing'] ?? 0),
            'fba_inbound_qty' => (int)($row['afn_inbound_shipped_quantity'] ?? 0),
            'inventory_plus_inbound_qty' => (int)(($row['afn_fulfillable_quantity'] ?? 0) + ($row['reserved_fc_transfers'] ?? 0) +
             ($row['reserved_fc_processing'] ?? 0) + ($row['afn_inbound_shipped_quantity'] ?? 0))
        ];
    } 

    /**
     * 计算价格相关字段
     * @param array $row 数据行
     * @return array
     */
    private function calculatePrices($row)
    {
        return [
            'fba_sellable_price' => (float)($row['afn_fulfillable_quantity_price'] ?? 0),
            'fba_pending_transfer_price' => (float)($row['reserved_fc_transfers_price'] ?? 0),
            'fba_transferring_price' => (float)($row['reserved_fc_processing_price'] ?? 0),
            'fba_inbound_price' => (float)($row['afn_inbound_shipped_quantity_price'] ?? 0),
            'inventory_plus_inbound_price' => (float)(($row['afn_fulfillable_quantity_price'] ?? 0) + ($row['fba_pending_transfer_price'] ?? 0) + 
            ($row['fba_transferring_price'] ?? 0) + ($row['fba_inbound_price'] ?? 0))
        ];
    }

    /**
     * 聚合数据到维度记录中
     * @param array &$dimensionData 维度数据（引用传递）
     * @param array $quantities 数量数据
     * @param array $prices 价格数据
     * @param array $row 原始数据行
     * @return void
     */
    private function aggregateData(&$dimensionData, $quantities, $prices, $row)
    {
        // 累加数量字段
        foreach ($quantities as $field => $value) {
            $dimensionData[$field] += $value;
        }

        // 店铺库存统计
        if (!empty($row['shop_type'])) {
            if (!isset($dimensionData['shop_detail'][$row['shop_type']])) {
                $dimensionData['shop_detail'][$row['shop_type']] = [
                    'fba_sellable_qty' => 0,
                    'fba_pending_transfer_qty' => 0,
                    'fba_transferring_qty' => 0,
                    'fba_inbound_qty' => 0,
                    'inventory_plus_inbound_qty' => 0,
                ];
            }
            $dimensionData['shop_detail'][$row['shop_type']]['fba_sellable_qty'] += $quantities['fba_sellable_qty'];
            $dimensionData['shop_detail'][$row['shop_type']]['fba_pending_transfer_qty'] += $quantities['fba_pending_transfer_qty'];
            $dimensionData['shop_detail'][$row['shop_type']]['fba_transferring_qty'] += $quantities['fba_transferring_qty'];
            $dimensionData['shop_detail'][$row['shop_type']]['fba_inbound_qty'] += $quantities['fba_inbound_qty'];
            $dimensionData['shop_detail'][$row['shop_type']]['inventory_plus_inbound_qty'] += $quantities['inventory_plus_inbound_qty'];
        }


        // 累加价格字段
        foreach ($prices as $field => $value) {
            $dimensionData[$field] += $value;
        }

        // 更新统计信息
        $this->updateStatistics($dimensionData, $row);
    }

    /**
     * 更新统计信息
     * @param array &$dimensionData 维度数据（引用传递）
     * @param array $row 原始数据行
     * @return void
     */
    private function updateStatistics(&$dimensionData, $row)
    {
        // 更新店铺列表
        if (!empty($row['sid']) && !in_array($row['sid'], $dimensionData['shop_list'])) {
            $dimensionData['shop_list'][] = $row['sid'];
        }

        // 更新站点列表
        if (!empty($row['country_code']) && !in_array($row['country_code'], $dimensionData['country_code_list'])) {
            $dimensionData['country_code_list'][] = $row['country_code'];
        }

        // 更新SKU列表
        if (!empty($row['sku']) && !in_array($row['sku'], $dimensionData['sku_list'])) {
            $dimensionData['sku_list'][] = $row['sku'];
        }

        // 更新品名列表
        if (!empty($row['product_name']) && !in_array($row['product_name'], $dimensionData['product_name_list'])) {
            $dimensionData['product_name_list'][] = $row['product_name'];
        }

        // 更新仓库列表
        if (!empty($row['warehouse_name']) && !in_array($row['warehouse_name'], $dimensionData['warehouse_list'])) {
            $dimensionData['warehouse_list'][] = $row['warehouse_name'];
        }

        // 更新seller_sku列表
        if (!empty($row['seller_sku']) && !in_array($row['seller_sku'], $dimensionData['seller_sku_list'])) {
            $dimensionData['seller_sku_list'][] = $row['seller_sku'];
        }

        // 更新fnsku列表
        if (!empty($row['fnsku']) && !in_array($row['fnsku'], $dimensionData['fnsku_list'])) {
            $dimensionData['fnsku_list'][] = $row['fnsku'];
        }

        // 更新分类列表
        if (!empty($row['category_text']) && !in_array($row['category_text'], $dimensionData['category_list'])) {
            $dimensionData['category_list'][] = $row['category_text'];
        }

        // 更新品牌列表
        if (!empty($row['product_brand_text']) && !in_array($row['product_brand_text'], $dimensionData['brand_list'])) {
            $dimensionData['brand_list'][] = $row['product_brand_text'];
        }

        // 合并操作用户ID
        if (!empty($row['operation_user_ids'])) {
            foreach ($row['operation_user_ids'] as $operationUserId) {
                if (!in_array($operationUserId, $dimensionData['operation_user_ids'])) {
                    $dimensionData['operation_user_ids'][] = $operationUserId;
                }
            }
        }

        // 合并备货用户ID
        if (!empty($row['stock_user_ids'])) {
            foreach ($row['stock_user_ids'] as $stockUserId) {
                if (!in_array($stockUserId, $dimensionData['stock_user_ids'])) {
                    $dimensionData['stock_user_ids'][] = $stockUserId;
                }
            }
        }
    }

    /**
     * 批量插入汇总数据到目标数据库（新版本）
     * @param object $targetDb 目标数据库连接
     * @param array $summaryData 汇总数据
     * @return array 插入结果统计
     */
    private function batchInsertSummaryDataNew($targetDb, $summaryData)
    {
        $result = ['inserted' => 0, 'updated' => 0];

        foreach ($summaryData as $data) {
            // 转换数组字段为JSON
            $list_key = ['shop_list', 'country_code_list', 'sku_list', 'product_name_list',
                'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list', 'operation_user_ids', 'stock_user_ids'];
            foreach ($list_key as $key) {
                if (is_array($data[$key])) {
                    $data[$key] = json_encode($data[$key], JSON_UNESCAPED_UNICODE);
                }
            }

            try {
                // 检查是否已存在相同维度的记录
                $existingId = $this->checkExistingRecord($targetDb, $data);

                if ($existingId) {
                    // 更新现有记录
                    $this->updateExistingRecord($targetDb, $existingId, $data);
                    $result['updated']++;
                } else {
                    // 插入新记录
                    $targetDb->table('fba_storage_summary')->insert($data);
                    $result['inserted']++;
                }
            } catch (\Exception $e) {
                error_log("插入汇总数据失败: " . $e->getMessage() . " Data: " . json_encode($data));
                throw $e;
            }
        }

        return $result;
    }



    /**
     * 检查是否存在相同维度的记录
     * @param object $targetDb 目标数据库连接
     * @param array $data 数据
     * @return int|false 存在则返回ID，否则返回false
     */
    private function checkExistingRecord($targetDb, $data)
    {
        $where = 'level_type = :level_type AND asin = :asin AND sync_date = :sync_date ';
        $whereData = [
            'level_type' => $data['level_type'],
            'asin' => $data['asin'],
            'sync_date' => $data['sync_date']
        ];

        // 根据层级添加额外的唯一性条件
        switch ($data['level_type']) {
            case 1: // 店铺级
                $where .= ' AND sku = :sku AND country_code = :country_code AND sid = :sid';
                $whereData['sku'] = $data['sku'];
                $whereData['country_code'] = $data['country_code'];
                $whereData['sid'] = $data['sid'];
                break;
            case 2: // 站点级
                $where .= ' AND sku = :sku AND country_code = :country_code';
                $whereData['sku'] = $data['sku'];
                $whereData['country_code'] = $data['country_code'];
                break;
            case 3: // SKU级
                $where .= ' AND sku = :sku';
                $whereData['sku'] = $data['sku'];
                break;
            case 4: // ASIN级
                // 只需要asin条件
                break;
        }

        $existing = $targetDb->table('fba_storage_summary')
            ->field('id')
            ->where($where, $whereData)
            ->one();

        return $existing ? $existing['id'] : false;
    }

    /**
     * 更新现有记录
     * @param object $targetDb 目标数据库连接
     * @param int $id 记录ID
     * @param array $data 新数据
     * @return void
     */
    private function updateExistingRecord($targetDb, $id, $data)
    {
        // 获取现有记录
        $existing = $targetDb->table('fba_storage_summary')
            ->where('id = :id', ['id' => $id])
            ->one();

        if (!$existing) {
            return;
        }

        // 累加数值字段
        $numericFields = [
            'fba_sellable_qty', 'fba_pending_transfer_qty', 'fba_transferring_qty',
            'fba_inbound_qty', 'inventory_plus_inbound_qty',
            'fba_sellable_price', 'fba_pending_transfer_price', 'fba_transferring_price',
            'fba_inbound_price', 'inventory_plus_inbound_price'
        ];

        $updateData = [];
        foreach ($numericFields as $field) {
            $updateData[$field] = (float)$existing[$field] + (float)$data[$field];
        }

        // 合并列表字段
        $listFields = ['shop_list', 'country_code_list', 'sku_list', 'product_name_list',
            'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list', 'operation_user_ids', 'stock_user_ids'];
        foreach ($listFields as $field) {
            $existingList = json_decode($existing[$field] ?? '[]', true) ?: [];
            $newList = json_decode($data[$field] ?? '[]', true) ?: [];
            $mergedList = array_unique(array_merge($existingList, $newList));
            $updateData[$field] = json_encode($mergedList, JSON_UNESCAPED_UNICODE);
        }

        // 执行更新
        $targetDb->table('fba_storage_summary')
            ->where('id = :id', ['id' => $id])
            ->update($updateData);
    }

    /**
     * 验证数据库连接
     * @param object $db 数据库连接对象
     * @param string $dbName 数据库名称
     * @return bool
     */
    private function validateDbConnection($db, $dbName)
    {
        try {
            if (!$db) {
                throw new \Exception("$dbName 数据库连接失败");
            }
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 验证输入参数
     * @param string $syncDate 同步日期
     * @param int $batchSize 批量大小
     * @return void
     * @throws \Exception
     */
    private function validateInputParams($syncDate, $batchSize)
    {
        // 验证同步日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $syncDate)) {
            throw new \Exception("同步日期格式错误，应为 YYYY-MM-DD 格式");
        }

        // 验证批量大小
        if (!is_int($batchSize) || $batchSize <= 0 || $batchSize > 10000) {
            throw new \Exception("批量大小必须为1-10000之间的整数");
        }

        // 验证日期不能是未来日期
        if (strtotime($syncDate) > time()) {
            throw new \Exception("同步日期不能是未来日期");
        }
    }

    /**
     * 安全执行数据库操作
     * @param callable $operation 数据库操作函数
     * @param string $operationName 操作名称
     * @param int $maxRetries 最大重试次数
     * @return mixed
     * @throws \Exception
     */
    private function safeDbOperation($operation, $operationName, $maxRetries = 3)
    {
        $retries = 0;
        $lastException = null;

        while ($retries < $maxRetries) {
            try {
                return $operation();
            } catch (\Exception $e) {
                $lastException = $e;
                $retries++;

                if ($retries < $maxRetries) {
                    // 等待一段时间后重试
                    usleep(100000 * $retries); // 递增等待时间
                }
            }
        }

        // 所有重试都失败了

        throw new \Exception("$operationName 操作失败: " . $lastException->getMessage());
    }

    /**
     * 获取内存使用情况
     * @return array
     */
    private function getMemoryUsage()
    {
        return [
            'current' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
            'peak' => round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB'
        ];
    }

    /**
     * 检查内存使用是否超限
     * @param int $limitMB 内存限制（MB）
     * @return bool
     */
    private function checkMemoryLimit($limitMB = 512)
    {
        $currentUsage = memory_get_usage() / 1024 / 1024;
        if ($currentUsage > $limitMB) {
            return false;
        }
        return true;
    }
}
