<?php
namespace plugins\logistics\controller;

use core\lib\ExceptionError;
use plugins\logistics\models\returnPlanModel;
use plugins\logistics\form\ApprovalForm;

class returnPlanController
{
    private $model;
    
    public function __construct()
    {
        $this->model = new returnPlanModel();
    }
    
    /**
     * 获取回货计划列表
     */
    public function getList()
    {
        try {
            $params = [
                'plan_no' => $_GET['plan_no'] ?? '',
                'sku' => $_GET['sku'] ?? '',
                'site' => $_GET['site'] ?? '',
                'week_period' => $_GET['week_period'] ?? '',
                'status' => $_GET['status'] ?? '',
                'page' => (int)($_GET['page'] ?? 1),
                'page_size' => (int)($_GET['page_size'] ?? 20)
            ];
            
            $result = $this->model->getList($params);
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 批量创建回货计划
     */
    public function batchCreate()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['plans']) || !is_array($input['plans'])) {
                throw new ExceptionError("计划数据不能为空");
            }
            
            // 验证数据
            foreach ($input['plans'] as $plan) {
                if (empty($plan['sku'])) {
                    throw new ExceptionError("SKU不能为空");
                }
                if (empty($plan['site'])) {
                    throw new ExceptionError("站点不能为空");
                }
                if (empty($plan['week_period'])) {
                    throw new ExceptionError("周期不能为空");
                }
                if (!isset($plan['demand_qty']) || $plan['demand_qty'] <= 0) {
                    throw new ExceptionError("需求量必须大于0");
                }
            }
            
            $planIds = $this->model->batchCreate($input['plans']);
            
            return [
                'code' => 200,
                'message' => '创建成功',
                'data' => ['plan_ids' => $planIds]
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 提交审批
     */
    public function submitApproval()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['plan_no'])) {
                throw new ExceptionError("计划单号不能为空");
            }
            
            $result = $this->model->submitApproval($input['plan_no']);
            
            return [
                'code' => 200,
                'message' => '提交审批成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 编辑回货计划
     */
    public function edit()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['id'])) {
                throw new ExceptionError("计划ID不能为空");
            }
            
            // 检查计划状态
            $plan = $this->model->getById($input['id']);
            if (!$plan) {
                throw new ExceptionError("计划不存在");
            }
            
            if ($plan['status'] !== 'draft') {
                throw new ExceptionError("只有草稿状态的计划才能编辑");
            }
            
            $updateData = [];
            if (isset($input['sku'])) {
                $updateData['sku'] = $input['sku'];
            }
            if (isset($input['site'])) {
                $updateData['site'] = $input['site'];
            }
            if (isset($input['week_period'])) {
                $updateData['week_period'] = $input['week_period'];
            }
            if (isset($input['demand_qty'])) {
                $updateData['demand_qty'] = $input['demand_qty'];
            }
            if (isset($input['remark'])) {
                $updateData['remark'] = $input['remark'];
            }
            
            $result = $this->model->update($input['id'], $updateData);
            
            return [
                'code' => 200,
                'message' => '编辑成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 删除回货计划
     */
    public function delete()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['id'])) {
                throw new ExceptionError("计划ID不能为空");
            }
            
            // 检查计划状态
            $plan = $this->model->getById($input['id']);
            if (!$plan) {
                throw new ExceptionError("计划不存在");
            }
            
            if ($plan['status'] !== 'draft') {
                throw new ExceptionError("只有草稿状态的计划才能删除");
            }
            
            $result = $this->model->delete($input['id']);
            
            return [
                'code' => 200,
                'message' => '删除成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 作废回货计划
     */
    public function cancel()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['plan_no'])) {
                throw new ExceptionError("计划单号不能为空");
            }
            
            $result = $this->model->cancel($input['plan_no']);
            
            return [
                'code' => 200,
                'message' => '作废成功',
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取审批状态
     */
    public function getApprovalStatus()
    {
        try {
            $planNo = $_GET['plan_no'] ?? '';
            
            if (empty($planNo)) {
                throw new ExceptionError("计划单号不能为空");
            }
            
            // 获取计划信息
            $plan = $this->model->getByPlanNo($planNo);
            if (!$plan) {
                throw new ExceptionError("计划不存在");
            }
            
            // 获取审批状态
            $approvalStatus = null;
            if ($plan['approval_instance_id']) {
                $approvalStatus = ApprovalForm::getApprovalStatus('return_plan', $plan['id']);
            }
            
            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'plan' => $plan,
                    'approval_status' => $approvalStatus
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 处理审批结果（由审批系统回调）
     */
    public function handleApprovalResult()
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $businessType = $input['business_type'] ?? '';
            $businessId = $input['business_id'] ?? 0;
            $approved = $input['approved'] ?? false;
            
            if ($businessType !== 'return_plan') {
                throw new ExceptionError("业务类型不匹配");
            }
            
            if ($approved) {
                // 审批通过
                $plan = $this->model->getById($businessId);
                if ($plan) {
                    $this->model->approvalPassed($plan['plan_no']);
                }
            } else {
                // 审批拒绝
                $this->model->updateStatus($businessId, 'rejected');
            }
            
            return [
                'code' => 200,
                'message' => '处理成功',
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
}
